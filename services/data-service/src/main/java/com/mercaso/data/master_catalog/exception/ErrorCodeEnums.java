package com.mercaso.data.master_catalog.exception;

public enum ErrorCodeEnums {

  COMMON_CODE("000000"),

  //Master Catalog Raw data
  MASTER_CATALOG_RAW_DATA_NOT_FOUND("001001"),

  IMAGE_MANAGEMENT_ITEM_IMAGE_UPLOAD_ALREADY("002001"),

  UNSUPPORTED_IMAGE_MANAGEMENT_ITEM_IMAGE_MIME_TYPE("002002"),

  INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_PATH("002003"),

  UPLOAD_IMAGE_MANAGEMENT_ITEM_IMAGE_FAILURE("002004"),

  //Unexpected error
  UNEXPECTED_ERROR("020001");


  private String code;

  ErrorCodeEnums(String code) {
    this.code = code;
  }

  public String getCode() {
    return code;
  }
}
