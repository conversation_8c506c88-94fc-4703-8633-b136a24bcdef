package com.mercaso.data.metrics.controller;

import com.mercaso.data.metrics.enums.UploadFileType;
import com.mercaso.data.metrics.service.MetricsUpLoadService;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/metrics/document/v1")
@RequiredArgsConstructor
@Slf4j
public class MetricsUploadController {

    private final MetricsUpLoadService metricsUpLoadService;

    @PreAuthorize("hasAnyAuthority('ds:write:metrics')")
    @PostMapping("/upload/{fileType}")
    public ResponseEntity<Void> upload(@PathVariable UploadFileType fileType, @RequestParam("file") MultipartFile file)
        throws IOException {
        log.info("Received file for type: {}", fileType);
        metricsUpLoadService.upload(fileType, file);
        return ResponseEntity.ok().build();
    }

}
