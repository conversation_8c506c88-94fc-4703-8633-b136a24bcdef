package com.mercaso.data.third_party.mapper;

import com.mercaso.data.third_party.dto.finale.FinaleAvailableStockDto;
import com.mercaso.data.third_party.dto.finale.FinaleAvailableStockItemsOnHandDto;
import com.mercaso.data.third_party.dto.finale.FinaleProductDataResponseDto;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.mapstruct.*;
import java.sql.Timestamp;
import org.springframework.util.ObjectUtils;

@Mapper(componentModel = "spring", imports = {Timestamp.class})
public interface FinaleAvailableStockMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "mfcQoh", expression = "java(removeComma(node.getNode().getMfcQoh()))")
    @Mapping(target = "name", source = "node.node.name")
    @Mapping(target = "productUrl", source = "node.node.productUrl")
    @Mapping(target = "recordLastUpdated", expression = "java(Timestamp.valueOf(node.getNode().getRecordLastUpdated()))")
    @Mapping(target = "reservationsQoh", expression = "java(removeComma(node.getNode().getReservationsQoh()))")
    @Mapping(target = "shopifyQoh", expression = "java(removeComma(node.getNode().getShopifyQoh()))")
    @Mapping(target = "sku", source = "node.node.sku")
    @Mapping(target = "stockSublocations", source = "node.node.stockSublocations")
    @Mapping(target = "stockItemsOnHand", source = "node.node.stockItemsOnHand.edges")
    FinaleAvailableStockDto toFinaleAvailableStockDto(FinaleProductDataResponseDto.Edge node);

    @Mapping(
        target = "quantityOnHand",
        expression = "java(mapQuantityOnHand(entity.getNode().getQuantityOnHand()))"
    )
    @Mapping(target = "subLocation", source = "entity.node.sublocation")
    FinaleAvailableStockItemsOnHandDto toFinaleAvailableStockItemsOnHandDto(FinaleProductDataResponseDto.StockItemEdge entity);

    @Mapping(target = "name", source = "name")
    @Mapping(target = "facilityUrl", source = "facilityUrl")
    FinaleAvailableStockItemsOnHandDto.Location toLocation(FinaleProductDataResponseDto.Sublocation sublocation);

    default Integer removeComma(String value) {
        if (ObjectUtils.isEmpty(value)){
            return 0;
        }

        String withoutCommas = value.replace(",", "").trim();

        BigDecimal bd = new BigDecimal(withoutCommas);

        if (bd.stripTrailingZeros().scale() > 0) {
            bd = bd.setScale(0, RoundingMode.UP);
        }

        return bd.intValueExact();
    }

    default Long mapQuantityOnHand(String quantityOnHand) {
        if (quantityOnHand == null || quantityOnHand.trim().isEmpty()) {
            return 0L;
        }
        BigDecimal bd = new BigDecimal(quantityOnHand.trim());

        if (bd.stripTrailingZeros().scale() > 0) {
            bd = bd.setScale(0, RoundingMode.UP);
        }
        return bd.longValue();
    }
}