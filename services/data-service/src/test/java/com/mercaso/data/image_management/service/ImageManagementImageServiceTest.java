package com.mercaso.data.image_management.service;

import com.mercaso.data.image_management.dto.Result;
import com.mercaso.data.image_management.entity.ImageManagementImage;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import com.mercaso.data.image_management.exception.ImageManagementImageException;
import com.mercaso.data.image_management.repository.ImageManagementImageRepository;
import com.mercaso.data.image_management.repository.ImageManagementItemImageRepository;
import com.mercaso.data.image_management.service.impl.ImageManagementImageServiceImpl;
import com.mercaso.data.master_catalog.adaptor.ImsClientAdaptor;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.document.operations.operations.DocumentOperations;
import java.io.IOException;
import java.time.Instant;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ImageManagementImageServiceTest {

  private final MultipartFile file = mock(MultipartFile.class);

  private final ImsClientAdaptor imsClientAdaptor = mock(ImsClientAdaptor.class);
  private final DocumentOperations documentOperations = mock(DocumentOperations.class);
  private final ImageManagementImageRepository imageManagementRepository = mock(ImageManagementImageRepository.class);
  private final ImageManagementItemImageRepository imageManagementItemImageRepository = mock(
      ImageManagementItemImageRepository.class);

  private final ImageManagementImageService service = new ImageManagementImageServiceImpl(
      imsClientAdaptor, documentOperations, imageManagementRepository, imageManagementItemImageRepository);

  @Test
  public void uploadAndSave_ShouldBeSuccess() throws IOException {
    String imagePath = "/2025-03-31/17506195178130/17506195178130_Front_2.png";

    ImageManagementImage image = ImageManagementImage.builder()
        .id(UUID.randomUUID())
        .fileName(file.getName())
        .filePath(imagePath)
        .shotAt(Instant.now())
        .fileSize(file.getSize())
        .mimeType("png")
        .createdBy("admin")
        .build();

    when(imageManagementRepository.save(any())).thenReturn(image);
    when(file.getOriginalFilename()).thenReturn("17506195178130_Front_2.jpg");
    when(file.getBytes()).thenReturn(new byte[100]);
    when(file.getSize()).thenReturn(100L);

    String code = service.uploadAndSave(
        imagePath, ImageTypeEnum.RAW, file
    );

    assert ErrorCodeEnums.COMMON_CODE.getCode().equals(code);
  }

  @Test
  public void uploadAndSave_InvalidPath_ThrowsException() {
    String invalidPath = "test_path";

    ImageManagementImageException exception = Assertions.assertThrows(ImageManagementImageException.class,
        () -> service.uploadAndSave(invalidPath, ImageTypeEnum.RAW, file));
    assert ErrorCodeEnums.INVALID_IMAGE_MANAGEMENT_ITEM_IMAGE_PATH.getCode().equals(exception.getCode());
  }

  @Test
  void uploadAndSave_UnsupportedImageMimeType_ThrowsException() {
    String unSupportImageMimeTypePath = "/2025-03-31/17506195178130/17506195178130_Front_2.pngz";

    ImageManagementImageException exception = Assertions.assertThrows(ImageManagementImageException.class,
        () -> service.uploadAndSave(unSupportImageMimeTypePath, ImageTypeEnum.RAW, file));

    assert ErrorCodeEnums.UNSUPPORTED_IMAGE_MANAGEMENT_ITEM_IMAGE_MIME_TYPE.getCode().equals(exception.getCode());
  }

  @Test
  void uploadAndSave_UploadFailed_ThrowsException() throws IOException {
    String imagePath = "/2025-03-31/17506195178130/17506195178130_Front_2.png";
    when(file.getBytes()).thenThrow(new IOException("IO Error"));

    ImageManagementImageException exception = assertThrows(ImageManagementImageException.class,
        () -> service.uploadAndSave(imagePath, ImageTypeEnum.RAW, file)
    );

    assert ErrorCodeEnums.UPLOAD_IMAGE_MANAGEMENT_ITEM_IMAGE_FAILURE.getCode().equals(exception.getCode());
  }
}
