package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import java.time.Instant;

import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.bulkexportrecords.enums.SendStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class BulkExportRecordsCommand extends BaseCommand {


    private String fileName;

    private Instant searchTime;

    private Instant sendEmailTime;

    private String customFilter;

    private SendStatus sendStatus;

    private ExportType exportType;

}
