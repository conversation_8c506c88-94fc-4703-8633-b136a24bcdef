package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import com.mercaso.ims.application.dto.ItemDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateItemVersionCommand extends BaseCommand {

    private final UUID itemId;

    private String skuNumber;

    private ItemDto itemDto;

    private Integer versionNumber;

}
