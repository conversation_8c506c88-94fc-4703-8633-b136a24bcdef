package com.mercaso.ims.application.searchservice;

import com.mercaso.ims.application.dto.ItemPriceExceptionRecordListDto;
import com.mercaso.ims.application.dto.VendorItemCostExceptionRecordListDto;
import com.mercaso.ims.application.query.ItemPriceExceptionRecordQuery;
import com.mercaso.ims.application.query.VendorItemCostExceptionRecordQuery;


public interface ExceptionRecordSearchApplicationService {


    ItemPriceExceptionRecordListDto searchItemPriceExceptionRecord(ItemPriceExceptionRecordQuery query);

    VendorItemCostExceptionRecordListDto searchVendorItemCostExceptionRecord(VendorItemCostExceptionRecordQuery query);

}
