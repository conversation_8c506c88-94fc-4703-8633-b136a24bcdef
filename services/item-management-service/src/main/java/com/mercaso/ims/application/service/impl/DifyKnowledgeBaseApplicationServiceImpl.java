package com.mercaso.ims.application.service.impl;

import com.google.common.collect.Lists;
import com.mercaso.ims.application.dto.CategoryGroupDto;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DifyKnowledgeBaseApplicationService;
import com.mercaso.ims.infrastructure.external.dify.DifyApiClient;
import com.mercaso.ims.infrastructure.external.dify.dto.*;
import com.mercaso.ims.infrastructure.util.DifyCategoryContentGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class DifyKnowledgeBaseApplicationServiceImpl implements DifyKnowledgeBaseApplicationService {

    private final DifyApiClient difyApiClient;
    private final CategoryApplicationService categoryApplicationService;
    private final DifyCategoryContentGenerator difyCategoryContentGenerator;

    @Override
    public void syncKnowledgeBaseByCategory(UUID leafCategoryId) throws IOException {
        log.info("[syncKnowledgeBaseByCategory] Starting sync for category ID: {}", leafCategoryId);
        
        CategoryGroupDto categoryGroup = categoryApplicationService.getCategoryTreeByLeafCategoryId(leafCategoryId);
        if (categoryGroup == null) {
            log.warn("[syncKnowledgeBaseByCategory] Category not found for ID: {}", leafCategoryId);
            return;
        }

        String keyword = leafCategoryId.toString();
        DifyDocumentSegmentQueryResponseDto queryResponse = queryDocumentSegmentsByKeyword(keyword);

        if (queryResponse != null && !CollectionUtils.isEmpty(queryResponse.getData()) && queryResponse.getTotal() > 0) {
            log.info("[syncKnowledgeBaseByCategory] Found {} existing segments, updating...", queryResponse.getTotal());
            updateExistingSegments(queryResponse.getData(), categoryGroup, Boolean.TRUE);
        } else {
            log.info("[syncKnowledgeBaseByCategory] No existing segments found, creating new segment...");
            createNewSegment(categoryGroup);
        }
        
        log.info("[syncKnowledgeBaseByCategory] Sync completed for category ID: {}", leafCategoryId);
    }

    @Override
    public DifyDocumentSegmentQueryResponseDto queryDocumentSegmentsByKeyword(String keyword) throws IOException {
        log.info("[queryDocumentSegmentsByKeyword] Querying with keyword: {}", keyword);
        return difyApiClient.queryDocumentSegmentsByKeyword(keyword);
    }

    @Override
    public void deletedKnowledgeBaseSegmentsByCategory(UUID leafCategoryId) throws IOException {
        log.info("[deletedKnowledgeBaseSegmentsByCategory] Querying with keyword: {}", leafCategoryId);

        CategoryGroupDto categoryGroup = categoryApplicationService.getCategoryTreeByLeafCategoryId(leafCategoryId);

        String keyword = leafCategoryId.toString();
        DifyDocumentSegmentQueryResponseDto queryResponse = queryDocumentSegmentsByKeyword(keyword);

        if (queryResponse != null && !CollectionUtils.isEmpty(queryResponse.getData()) && queryResponse.getTotal() > 0) {
          log.info("[deletedKnowledgeBaseSegmentsByCategory] Found {} existing segments, updating...", queryResponse.getTotal());
          updateExistingSegments(queryResponse.getData(), categoryGroup, Boolean.FALSE);
        }
    }

    private void updateExistingSegments(List<DifyDocumentSegmentDto> existingSegments, CategoryGroupDto categoryGroup, Boolean enabled) throws IOException {
        for (DifyDocumentSegmentDto segment : existingSegments) {
            log.info("[updateExistingSegments] Updating segment ID: {}", segment.getId());
            String newContent;
            List<String> newKeywords;
            if (Boolean.TRUE.equals(enabled)) {
                newContent = difyCategoryContentGenerator.generateCategoryContent(categoryGroup);
                newKeywords = difyCategoryContentGenerator.generateKeywords(newContent);
            } else {
                newContent = segment.getContent();
                newKeywords = segment.getKeywords();
            }

            if (!StringUtils.hasText(newContent)) {
                log.error("[updateExistingSegments] No content found for category: {}", categoryGroup.getClazzName());
                continue;
            }
            
            DifyDocumentSegmentUpdateRequestDto updateRequest = DifyDocumentSegmentUpdateRequestDto.builder()
                    .content(newContent)
                    .keywords(newKeywords)
                    .enabled(enabled)
                    .build();

            DifyDocumentSegmentUpdateRequestBodyDto updateRequestBody = DifyDocumentSegmentUpdateRequestBodyDto.builder().segment(updateRequest).build();

            difyApiClient.updateDocumentSegment(segment.getId(), updateRequestBody);
            log.info("[updateExistingSegments] Successfully updated segment ID: {}", segment.getId());
        }
    }

    private void createNewSegment(CategoryGroupDto categoryGroup) throws IOException {
        log.info("[createNewSegment] Creating new segment for category: {}", categoryGroup.getClazzName());
        
        String content = difyCategoryContentGenerator.generateCategoryContent(categoryGroup);
        List<String> keywords = difyCategoryContentGenerator.generateKeywords(content);

        if (!StringUtils.hasText(content)) {
            log.error("[createNewSegment] No content found for category: {}", categoryGroup.getClazzName());
            return;
        }
        
        DifyDocumentSegmentCreateRequestDto createRequest = DifyDocumentSegmentCreateRequestDto.builder()
                .content(content)
                .keywords(keywords)
                .enabled(true)
                .build();

        DifyDocumentSegmentCreateRequestBodyDto createRequestBody = DifyDocumentSegmentCreateRequestBodyDto.builder().segments(Lists.newArrayList(createRequest)).build();

        difyApiClient.createDocumentSegment(createRequestBody);
        log.info("[createNewSegment] Successfully created new segment for category: {}", categoryGroup.getClazzName());
    }
}
