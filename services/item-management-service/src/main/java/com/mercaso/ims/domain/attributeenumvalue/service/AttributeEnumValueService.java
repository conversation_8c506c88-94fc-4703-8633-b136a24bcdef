package com.mercaso.ims.domain.attributeenumvalue.service;

import com.mercaso.ims.domain.attributeenumvalue.AttributeEnumValue;

import java.util.List;
import java.util.UUID;

public interface AttributeEnumValueService {

    AttributeEnumValue updateAttributeEnumValueSortOrder(UUID attributeEnumValueId, Float sortOrder);

    AttributeEnumValue findById(UUID id);

    List<AttributeEnumValue> findByAttributeId(UUID attributeId);

    AttributeEnumValue save(AttributeEnumValue attributeEnumValue);

    AttributeEnumValue update(AttributeEnumValue attributeEnumValue);

    void delete(UUID id);
}
