package com.mercaso.ims.domain.itemsyncinfo.service;

import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatus;
import java.util.UUID;

public interface ItemAdjustmentSyncStatusService {

    ItemAdjustmentSyncStatus findByBusinessEventId(UUID businessEventId);

    ItemAdjustmentSyncStatus save(ItemAdjustmentSyncStatus itemSyncInfo);

    ItemAdjustmentSyncStatus update(ItemAdjustmentSyncStatus itemSyncInfo);

    void saveItemAdjustmentSyncFiledStatus(UUID eventId);

    void saveItemAdjustmentSyncSuccessStatus(UUID eventId);

}
