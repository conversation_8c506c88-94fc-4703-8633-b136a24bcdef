package com.mercaso.ims.domain.vendorpoanalyzerecord.service;

import com.mercaso.ims.domain.vendorpoanalyzerecord.VendorPoAnalyzeRecord;
import java.util.UUID;

public interface VendorPoAnalyzeRecordService {

    VendorPoAnalyzeRecord findById(UUID id);

    VendorPoAnalyzeRecord save(VendorPoAnalyzeRecord vendorPoAnalyzeRecord);

    VendorPoAnalyzeRecord update(VendorPoAnalyzeRecord vendorPoAnalyzeRecord);

    VendorPoAnalyzeRecord findByItemCostCollectionId(UUID itemCostCollectionId);

    VendorPoAnalyzeRecord findByOriginalFileName(String originalFileName);

}
