package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import java.math.BigDecimal;
import java.time.Instant;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class ItemReportData {

    @ExcelProperty("Status")
    private String status;

    @ExcelProperty("Notes")
    @ColumnWidth(30)
    private String notes;

    @ExcelProperty("SKU")
    @ColumnWidth(20)
    private String sku;

    @ExcelProperty("Title")
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    @ColumnWidth(50)
    private String title;

    @ExcelProperty("New Description")
    @ColumnWidth(50)
    private String newDescription;

    @ExcelProperty("Package Size")
    private Integer packageSize;

    @ExcelProperty("Brand")
    @ColumnWidth(20)
    private String brand;

    @ExcelProperty("Tags")
    @ColumnWidth(30)
    private String tags;

    @ExcelProperty("Company ID")
    private String companyId;

    @ExcelProperty("Location ID")
    private String locationId;

    @ExcelProperty("Case Length")
    private Double caseLength;

    @ExcelProperty("Case Width")
    private Double caseWidth;

    @ExcelProperty("Case Height")
    private Double caseHeight;

    @ExcelProperty("Case Weight")
    private String caseWeight;

    @ExcelProperty("Each Weight")
    private String eachWeight;

    @ExcelProperty("Item Size")
    private String itemSize;

    @ExcelProperty("Bottle Size")
    private String bottleSize;

    @ExcelProperty("Cooler")
    private String cooler;

    @ExcelProperty("Department")
    @ColumnWidth(20)
    private String department;

    @ExcelProperty("Category")
    @ColumnWidth(20)
    private String category;

    @ExcelProperty("Sub-Category")
    @ColumnWidth(20)
    private String subCategory;

    @ExcelProperty("Class")
    @ColumnWidth(20)
    private String classType;

    @ExcelProperty("Each UPC")
    private String eachUpc;

    @ExcelProperty("Case UPC")
    private String caseUpc;

    @ExcelProperty("Direct Primary")
    private String directPrimary;

    @ExcelProperty("Direct Primary's SKU")
    private String directPrimarySku;

    @NumberFormat("$0.00")
    @ExcelProperty("Direct Primary's Cost")
    private BigDecimal directPrimaryCost;

    @ExcelProperty("JIT Primary")
    private String jitPrimary;

    @ExcelProperty("JIT Primary's SKU")
    private String jitPrimarySku;

    @NumberFormat("$0.00")
    @ExcelProperty("JIT Primary's Cost")
    private BigDecimal jitPrimaryCost;

    @ExcelProperty("JIT Primary's Availability")
    private Boolean jitPrimaryAvailability;

    @ExcelProperty("Price Group")
    private String priceGroup;

    @NumberFormat("$0.00")
    @ExcelProperty("Reg. Price")
    private BigDecimal regPrice;

    @NumberFormat("$0.00")
    @ExcelProperty("Individual Price")
    private BigDecimal individualPrice;

    @NumberFormat("$0.00")
    @ExcelProperty("Reg Plus CRV Price")
    private BigDecimal regPlusCrvPrice;

    @NumberFormat("$0.00")
    @ExcelProperty("CRV Price")
    private BigDecimal crvPrice;

    @ExcelProperty("CRV flag")
    private Boolean crvFlag;

    @ExcelProperty("Promo Flag")
    private Boolean promoFlag;

    @NumberFormat("$0.00")
    @ExcelProperty("Promotion Price")
    private BigDecimal promotionPrice;

    @NumberFormat("$0.00")
    @ExcelProperty("Promotion Individual Price")
    private BigDecimal promotionIndividualPrice;

    @NumberFormat("$0.00")
    @ExcelProperty("Promotion Price Plus CRV")
    private BigDecimal promotionPricePlusCrv;

    @ExcelProperty("Promo Begin At")
    @ColumnWidth(20)
    private String promoBeginAt;

    @ExcelProperty("Promo End At")
    @ColumnWidth(20)
    private String promoEndAt;

    @ExcelProperty("Sales (1 WOS)")
    private Long sales1WOS;

    @ExcelProperty("Sales (4 WOS)")
    private Long sales4WOS;

    @ExcelProperty("Grade")
    private String grade;

    @ExcelProperty("Mercaso QoH")
    private Integer mercasoQoH;

    @ExcelProperty("Reserved Units")
    private Integer reservedUnits;

    @ExcelProperty("Supplier Inventory")
    private Integer supplierInventory;

    @ExcelProperty("Photo")
    private String photo;
}