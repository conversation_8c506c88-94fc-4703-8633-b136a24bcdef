package com.mercaso.ims.infrastructure.excel.listener;

import static com.mercaso.ims.application.service.impl.CategoryApplicationServiceImpl.DEPTH_TO_GRANDPARENT;
import static com.mercaso.ims.application.service.impl.CategoryApplicationServiceImpl.DEPTH_TO_GREAT_GRANDPARENT;
import static com.mercaso.ims.application.service.impl.CategoryApplicationServiceImpl.DEPTH_TO_PARENT;
import static com.mercaso.ims.domain.vendor.VendorConstant.NO_BACKUP;
import static com.mercaso.ims.domain.vendor.VendorConstant.NO_SECONDARY;
import static com.mercaso.ims.infrastructure.util.FormatUtils.cleanInput;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ConverterUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.category.CategoryConstant;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateOrUpdateItemRequestData;
import com.mercaso.ims.infrastructure.excel.data.ItemAdjustmentRequestData;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;


@Getter
@SuperBuilder
@Slf4j
public abstract class ItemAdjustmentRequestDataListener<T extends ItemAdjustmentRequestData> implements ReadListener<T> {

    protected ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;
    protected ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;
    protected ItemRepository itemRepository;
    protected UUID itemAdjustmentRequestId;
    @Builder.Default
    protected Integer createdRowCount = 0;
    @Builder.Default
    protected Integer modifiedRowCoun = 0;
    @Builder.Default
    protected Integer deletedRowCount = 0;
    protected VendorRepository vendorRepository;
    protected VendorItemRepository vendorItemRepository;
    protected CategoryApplicationService categoryApplicationService;
    protected FeatureFlagsManager featureFlagsManager;
    protected BrandRepository brandRepository;

    private Map<String, Object> categoryData;


    ItemAdjustmentRequestDataListener(UUID itemAdjustmentRequestId,
        ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
        ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
        ItemRepository itemRepository,
        VendorRepository vendorRepository,
        VendorItemRepository vendorItemRepository,
        CategoryApplicationService categoryApplicationService,
        FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository
    ) {
        this.itemAdjustmentRequestId = itemAdjustmentRequestId;
        this.itemAdjustmentRequestApplicationService = itemAdjustmentRequestApplicationService;
        this.itemAdjustmentRequestDetailApplicationService = itemAdjustmentRequestDetailApplicationService;
        this.itemRepository = itemRepository;
        this.createdRowCount = 0;
        this.modifiedRowCoun = 0;
        this.deletedRowCount = 0;
        this.vendorRepository = vendorRepository;
        this.vendorItemRepository = vendorItemRepository;
        this.categoryApplicationService = categoryApplicationService;
        this.featureFlagsManager = featureFlagsManager;
        this.brandRepository = brandRepository;
        loadCategoryData();
    }

    private void loadCategoryData() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            ClassPathResource resource = new ClassPathResource("json/category.json");
            this.categoryData = mapper.readValue(resource.getInputStream(), new TypeReference<Map<String, Object>>() {
            });
        } catch (IOException e) {
            log.error("categoryData error :{}", e.getMessage(), e);
            this.categoryData = Collections.emptyMap();
        }
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        this.invokeHeadMap(ConverterUtils.convertToStringMap(headMap, context), context);
    }

    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
    }

    abstract ItemAdjustmentType getItemAdjustmentType();

    public void invoke(T t, AnalysisContext analysisContext) {
        log.info("The result of parsing a line of data :{}", SerializationUtils.serialize(t));
        try {
            switch (getItemAdjustmentType()) {
                case CREATE:
                    createdRowCount++;
                    break;
                case UPDATE:
                    modifiedRowCoun++;
                    break;
                case DELETE:
                    deletedRowCount++;
                    break;
                default:
                    break;
            }
            createItemAdjustmentRequestDetail(t);
        } catch (Exception e) {
            log.warn("parsing data error :{} ", e.getMessage(), e);
            throw new ImsBusinessException("parsing data error : " + e.getMessage(), e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        updateItemAdjustmentRequestInfo();
        log.info(" All data parsing completed！, createdRowCount, modifiedRowCoun, deletedRowCount {} :{} :{}",
            createdRowCount,
            modifiedRowCoun,
            deletedRowCount);
    }

    private void updateItemAdjustmentRequestInfo() {
        itemAdjustmentRequestApplicationService.addCountOfParsed(itemAdjustmentRequestId,
            createdRowCount,
            modifiedRowCoun,
            deletedRowCount);
    }

    private void createItemAdjustmentRequestDetail(T t) {
        if (t.getSku() == null) {
            log.warn("Parsing a line of data error : sku is null");
            return;
        }
        CreateItemAdjustmentRequestDetailCommand command = convertToCreateItemAdjustmentRequestDetailCommand(t);
        if (command == null) {
            return;
        }
        List<ItemAdjustmentFailureReason> failureReasons = validateInput(t);
        if (!CollectionUtils.isEmpty(failureReasons)) {
            String reason = failureReasons.stream()
                .map(ItemAdjustmentFailureReason::getReason)
                .reduce((a, b) -> a + "," + b)
                .orElse("");
            command.setStatus(ItemAdjustmentStatus.VALIDATION_FAILURE);
            command.setFailureReason(reason);
        }
        itemAdjustmentRequestDetailApplicationService.create(command);
    }

    abstract CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(T t);

    abstract List<ItemAdjustmentFailureReason> validateInput(T t);

    protected ItemAdjustmentFailureReason validateCRVFlag(String department, Boolean crvFlag) {
        if (crvFlag != null && crvFlag && department != null && !CategoryConstant.BEVERAGE.equals(department)) {
            return ItemAdjustmentFailureReason.INVALID_CRV_FLAG_FOR_DEPARTMENT;
        }
        return null;
    }

    protected ItemAdjustmentFailureReason validateStatus(String status) {
        if (StringUtils.isNotBlank(status) && AvailabilityStatus.fromString(status) == AvailabilityStatus.UNKNOWN) {
            return ItemAdjustmentFailureReason.INVALID_STATUS;
        }
        return null;
    }

    protected ItemAdjustmentFailureReason validateItemDescription(String description) {
        if (StringUtils.isNotBlank(description) && description.length() > 255) {
            return ItemAdjustmentFailureReason.DESCRIPTION_TOO_LONG;
        }
        return null;
    }

    protected ItemAdjustmentFailureReason validateUPC(String itemUpc) {
        if (StringUtils.isNotBlank(itemUpc) && Arrays.stream(itemUpc.split(","))
            .anyMatch(upc -> StringUtils.isNotBlank(upc) && !BarcodeValidator.isValidBarcode(upc))) {
            return ItemAdjustmentFailureReason.INVALID_UPC;
        }
        return null;
    }

    protected ItemAdjustmentFailureReason validateVendor(String vendorName) {
        if (StringUtils.isNotBlank(vendorName)) {
            Vendor vendor = vendorRepository.findByVendorName(vendorName);
            if (vendor == null) {
                return ItemAdjustmentFailureReason.VENDOR_NOT_FOUND;
            }
        }
        return null;
    }

    protected ItemAdjustmentFailureReason checkBackupVendor(String backupVendorName) {
        if (StringUtils.isBlank(backupVendorName)) {
            return null;
        }
        if (backupVendorName.equals(NO_SECONDARY) || backupVendorName.equals(NO_BACKUP)) {
            return null;
        }
        Vendor vendor = vendorRepository.findByVendorName(backupVendorName);
        if (vendor == null) {
            return ItemAdjustmentFailureReason.JIT_VENDOR_NOT_FOUND;

        }
        if (vendor.getExternalPicking() == null || !vendor.getExternalPicking()) {
            return ItemAdjustmentFailureReason.INVALID_JIT_VENDOR;
        }
        return null;
    }


    protected ItemAdjustmentFailureReason validateCategory(String department,
        String category,
        String subCategory,
        String clazz) {
        // Use feature flag to determine which validation method to use
        return validateCategoryNew(department, category, subCategory, clazz);
    }

    protected ItemAdjustmentFailureReason validateCategoryNew(String department,
        String category,
        String subCategory,
        String clazz) {
        if (StringUtils.isBlank(department) && StringUtils.isBlank(category) && StringUtils.isBlank(subCategory)
            && StringUtils.isBlank(clazz)) {
            return null;
        }

        // Get category tree by leaf category name
        Map<Integer, List<String>> categoryTreeByLeafCategoryName = categoryApplicationService.getCategoryTreeByLeafCategoryName(
            clazz);

        if (CollectionUtils.isEmpty(categoryTreeByLeafCategoryName)) {
            return ItemAdjustmentFailureReason.CATEGORY_NOT_FOUND;
        }

        // Get categories at each level
        List<String> subCategories = categoryTreeByLeafCategoryName.getOrDefault(DEPTH_TO_PARENT, Collections.emptyList());
        List<String> categories = categoryTreeByLeafCategoryName.getOrDefault(DEPTH_TO_GRANDPARENT, Collections.emptyList());
        List<String> departments = categoryTreeByLeafCategoryName.getOrDefault(DEPTH_TO_GREAT_GRANDPARENT,
            Collections.emptyList());

        // Validate department
        if (StringUtils.isNotBlank(department) && !departments.contains(department)) {
            return ItemAdjustmentFailureReason.DEPARTMENT_NOT_FOUND;
        }

        // Validate category
        if (StringUtils.isNotBlank(category) && !categories.contains(category)) {
            return ItemAdjustmentFailureReason.CATEGORY_NOT_MATCH;
        }

        // Validate sub-category
        if (StringUtils.isNotBlank(subCategory) && !subCategories.contains(subCategory)) {
            return ItemAdjustmentFailureReason.SUB_CATEGORY_NOT_MATCH;
        }

        // All validations passed
        return null;
    }


    protected List<ItemAdjustmentFailureReason> checkPackSize(Integer packSize, String itemDescription) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        if (getItemAdjustmentType() == ItemAdjustmentType.UPDATE && packSize == null) {
            return failureReasons;
        }
        if (packSize != null && StringUtils.isBlank(itemDescription)) {
            failureReasons.add(ItemAdjustmentFailureReason.INVALID_ITEM_DESCRIPTION);
        }
        if (packSize == null || packSize <= 0) {
            failureReasons.add(ItemAdjustmentFailureReason.INVALID_PACK_SIZE);
        }

        return failureReasons;
    }

    protected List<ItemAdjustmentFailureReason> checkBottleSize(String itemSize,
        String itemUnitMeasure,
        Boolean crvFlag,
        String itemDescription) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        if (itemSize != null && StringUtils.isBlank(itemUnitMeasure)) {
            failureReasons.add(ItemAdjustmentFailureReason.INVALID_BOTTLE_UNIT);
        }
        if (itemSize == null && StringUtils.isNotBlank(itemUnitMeasure)) {
            failureReasons.add(ItemAdjustmentFailureReason.INVALID_ITEM_SIZE);
        }

        if (Boolean.TRUE.equals(crvFlag)) {
            if (!VolumeUtil.isValidBottleUnit(itemUnitMeasure)) {
                failureReasons.add(ItemAdjustmentFailureReason.INVALID_BOTTLE_UNIT);
            }
            if (itemSize == null) {

                failureReasons.add(ItemAdjustmentFailureReason.INVALID_BOTTLE_SIZE);
            } else {
                try {
                    if (Float.parseFloat(itemSize) <= 0) {
                        failureReasons.add(ItemAdjustmentFailureReason.INVALID_BOTTLE_SIZE);
                    }
                } catch (NumberFormatException e) {
                    failureReasons.add(ItemAdjustmentFailureReason.INVALID_ITEM_SIZE);
                }
            }
            if (StringUtils.isBlank(itemDescription)) {
                failureReasons.add(ItemAdjustmentFailureReason.INVALID_ITEM_DESCRIPTION);
            }
        } else {
            if (StringUtils.isNotBlank(itemUnitMeasure) && !ItemSizeUtil.isValidItemSizeUnit(itemUnitMeasure)) {
                failureReasons.add(ItemAdjustmentFailureReason.INVALID_ITEM_SIZE_UNIT);
            }
        }
        return failureReasons;
    }

    protected ItemAdjustmentFailureReason checkRegPackPrice(BigDecimal price) {
        if (getItemAdjustmentType() == ItemAdjustmentType.UPDATE && price == null) {
            return null;
        }
        return (price == null || price.compareTo(BigDecimal.ZERO) <= 0) ? ItemAdjustmentFailureReason.INVALID_REG_PRICE : null;
    }

    protected List<ItemAdjustmentFailureReason> checkCompanyIdAndLocationId(String companyId, String locationId) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        if (StringUtils.isNotBlank(companyId) && !FormatUtils.isLongString(companyId)) {
            failureReasons.add(ItemAdjustmentFailureReason.INVALID_COMPANY_ID);
        }
        if (StringUtils.isNotBlank(locationId) && !FormatUtils.isLongString(locationId)) {
            failureReasons.add(ItemAdjustmentFailureReason.INVALID_LOCATION_ID);
        }
        return failureReasons;
    }

    protected ItemAdjustmentFailureReason checkItemExist(String sku) {
        Item item = itemRepository.findBySku(cleanInput(sku));
        if (item == null) {
            return ItemAdjustmentFailureReason.ITEM_NOT_FOUND;
        }
        return null;
    }

    protected ItemAdjustmentFailureReason checkImageUrl(String imageUrl) {
        if (StringUtils.isNotBlank(imageUrl)) {
            try {
                byte[] fileBytes = FileUtil.downloadFile(imageUrl);
                if (!FileUtil.isImageFile(fileBytes)) {
                    return ItemAdjustmentFailureReason.INVALID_IMAGE_URL;
                }
            } catch (ImsBusinessException e) {
                log.warn("Error checkImageUrl for externalImageUrl :{} ", imageUrl, e);
                return ItemAdjustmentFailureReason.PROCESS_IMAGE_URL_ERROR;
            }
        }
        return null;
    }

    protected ItemAdjustmentFailureReason checkBrandExist(String brandName) {
        if (StringUtils.isBlank(brandName)) {
            return null;
        }

        Brand brand = brandRepository.findByName(brandName.trim());
        if (brand == null) {
            return ItemAdjustmentFailureReason.BRAND_NOT_FOUND;
        }
        return null;
    }

    protected List<ItemAdjustmentFailureReason> newTemplateInputCheck(CreateOrUpdateItemRequestData createOrUpdateItemRequestData) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();

        ItemAdjustmentFailureReason validateStatusResult = validateStatus(createOrUpdateItemRequestData.getStatus());
        if (validateStatusResult != null) {
            failureReasons.add(validateStatusResult);
        }

        ItemAdjustmentFailureReason validateEachUpcResult = validateUPC(createOrUpdateItemRequestData.getEachUpc());
        if (validateEachUpcResult != null) {
            failureReasons.add(validateEachUpcResult);
        }
        ItemAdjustmentFailureReason validateCaseUpcResult = validateUPC(createOrUpdateItemRequestData.getCaseUpc());
        if (validateCaseUpcResult != null) {
            failureReasons.add(validateCaseUpcResult);
        }

        ItemAdjustmentFailureReason validateNewDescriptionResult = validateItemDescription(createOrUpdateItemRequestData.getNewDescription());
        if (validateNewDescriptionResult != null) {
            failureReasons.add(validateNewDescriptionResult);
        }

        ItemAdjustmentFailureReason validateDescriptionResult = validateItemDescription(createOrUpdateItemRequestData.getItemDescription());
        if (validateDescriptionResult != null) {
            failureReasons.add(validateDescriptionResult);
        }

        ItemAdjustmentFailureReason validateVendorResult = validateVendor(createOrUpdateItemRequestData.getPrimaryPoVendor());
        if (validateVendorResult != null) {
            failureReasons.add(validateVendorResult);
        }
        ItemAdjustmentFailureReason checkBackupVendorResult = checkBackupVendor(
            createOrUpdateItemRequestData.getPrimaryJitVendor());
        if (checkBackupVendorResult != null) {
            failureReasons.add(checkBackupVendorResult);
        }

        ItemAdjustmentFailureReason validateCategoryResult = validateCategory(createOrUpdateItemRequestData.getDepartment(),
            createOrUpdateItemRequestData.getCategory(),
            createOrUpdateItemRequestData.getSubCategory(),
            createOrUpdateItemRequestData.getClassType());
        if (validateCategoryResult != null) {
            failureReasons.add(validateCategoryResult);
        }
        List<ItemAdjustmentFailureReason> checkBottleSizeResult = checkBottleSize(createOrUpdateItemRequestData.getItemSize(),
            createOrUpdateItemRequestData.getItemUnitMeasure(),
            createOrUpdateItemRequestData.getCrvFlag(),
            createOrUpdateItemRequestData.getItemDescription());
        failureReasons.addAll(checkBottleSizeResult);

        List<ItemAdjustmentFailureReason> checkPackSizeResult = checkPackSize(createOrUpdateItemRequestData.getPackSize(),
            createOrUpdateItemRequestData.getItemDescription());
        failureReasons.addAll(checkPackSizeResult);

        ItemAdjustmentFailureReason checkRegPackPriceResult = checkRegPackPrice(createOrUpdateItemRequestData.getRegPricePack());
        if (checkRegPackPriceResult != null) {
            failureReasons.add(checkRegPackPriceResult);
        }

        List<ItemAdjustmentFailureReason> checkCompanyIdAndLocationIdResult = checkCompanyIdAndLocationId(
            createOrUpdateItemRequestData.getCompanyId(),
            createOrUpdateItemRequestData.getLocationId());

        ItemAdjustmentFailureReason checkImageUrlResult = checkImageUrl(createOrUpdateItemRequestData.getImageUrl());
        if (checkImageUrlResult != null) {
            failureReasons.add(checkImageUrlResult);
        }
        failureReasons.addAll(checkCompanyIdAndLocationIdResult);

        ItemAdjustmentFailureReason checkPrimaryVendorItemCostResult = validatePrimaryPoVendorItemCost(
            createOrUpdateItemRequestData.getPrimaryPoVendorItemCost());
        if (checkPrimaryVendorItemCostResult != null) {
            failureReasons.add(checkPrimaryVendorItemCostResult);
        }

        ItemAdjustmentFailureReason checkBackupVendorItemCostResult = validatePrimaryJitVendorItemCost(
            createOrUpdateItemRequestData.getPrimaryJitVendorItemCost());
        if (checkBackupVendorItemCostResult != null) {
            failureReasons.add(checkBackupVendorItemCostResult);
        }

        ItemAdjustmentFailureReason checkCaseWeightUnitResult = checkCaseWeightUnit(createOrUpdateItemRequestData.getCaseWeight(),
            createOrUpdateItemRequestData.getCaseWeightUnit());
        if (checkCaseWeightUnitResult != null) {
            failureReasons.add(checkCaseWeightUnitResult);
        }

        ItemAdjustmentFailureReason checkEachWeightUnitResult = checkEachWeightUnit(createOrUpdateItemRequestData.getEachWeight(),
                createOrUpdateItemRequestData.getEachWeightUnit());
        if (checkEachWeightUnitResult != null) {
            failureReasons.add(checkEachWeightUnitResult);
        }

        ItemAdjustmentFailureReason checkBrandResult = checkBrandExist(createOrUpdateItemRequestData.getBrand());
        if (checkBrandResult != null) {
            failureReasons.add(checkBrandResult);
        }

        return failureReasons;
    }

    protected ItemAdjustmentFailureReason validatePrimaryPoVendorItemCost(BigDecimal primaryPoVendorItemCost) {
        if (null != primaryPoVendorItemCost && primaryPoVendorItemCost.compareTo(BigDecimal.ZERO) <= 0) {
            return ItemAdjustmentFailureReason.PRIMARY_DIRECT_VENDOR_ITEM_COST_MUST_BE_GREATER_THAN_ZERO;
        }
        return null;
    }

    protected ItemAdjustmentFailureReason validatePrimaryJitVendorItemCost(BigDecimal primaryJitVendorItemCost) {
        if (null != primaryJitVendorItemCost && primaryJitVendorItemCost.compareTo(BigDecimal.ZERO) <= 0) {
            return ItemAdjustmentFailureReason.PRIMARY_JIT_VENDOR_ITEM_COST_MUST_BE_GREATER_THAN_ZERO;
        }
        return null;
    }

    protected ItemAdjustmentFailureReason checkCaseWeightUnit(Double weight, String weightUnit) {
        if (weight != null && StringUtils.isBlank(weightUnit)) {
            return ItemAdjustmentFailureReason.INVALID_CASE_WEIGHT_UNIT;
        }

        if (StringUtils.isNotBlank(weightUnit) && !CaseWeightUtil.isValidWeightUnit(weightUnit)) {
            return ItemAdjustmentFailureReason.INVALID_CASE_WEIGHT_UNIT;
        }
        return null;
    }

    protected ItemAdjustmentFailureReason checkEachWeightUnit(Double weight, String weightUnit) {
        if (weight != null && StringUtils.isBlank(weightUnit)) {
            return ItemAdjustmentFailureReason.INVALID_EACH_WEIGHT_UNIT;
        }

        if (StringUtils.isNotBlank(weightUnit) && !EachWeightUtil.isValidWeightUnit(weightUnit)) {
            return ItemAdjustmentFailureReason.INVALID_EACH_WEIGHT_UNIT;
        }
        return null;
    }

}
