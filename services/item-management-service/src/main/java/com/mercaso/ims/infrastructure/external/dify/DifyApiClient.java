package com.mercaso.ims.infrastructure.external.dify;

import java.io.*;
import java.util.Objects;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.dify.dto.*;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.DIFY_API_CALL_FAILED;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.DIFY_RESPONSE_BODY_NULL;

@Slf4j
@Component
@RequiredArgsConstructor
public class DifyApiClient {

  private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

  private final HttpClient client;

  @Value("${dify.api_key}")
  private String apiKey;

  @Value("${dify.base_url}")
  private String baseUrl;

  @Value("${dify.dataset_id}")
  private String datasetId;

  @Value("${dify.document_id}")
  private String documentId;

  @Value("${dify.knowledge_base_api_key}")
  private String knowledgeBaseApiKey;


  public DifyWorkflowResult callDifyWorkflow(String key, String value) throws IOException {
    ObjectNode payload = createWorkflowPayload(key, value);
    Request request = buildWorkflowRequest(payload);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      ObjectNode jsonNodes = extractWorkflowResult(response);
      return SerializationUtils.treeToValue(jsonNodes, DifyWorkflowResult.class);
    }
  }

  private ObjectNode createWorkflowPayload(String key, String value) {
    log.info("[createWorkflowPayload] key:{}, value:{}",key, value);
    ObjectNode payload = SerializationUtils.standardObjectMapper().createObjectNode();
    ObjectNode inputs = SerializationUtils.standardObjectMapper().createObjectNode();
    inputs.put(key, value);
    payload.set("inputs", inputs);
    payload.put("response_mode", "streaming");
    payload.put("user", SecurityUtil.getUserName());
    return payload;
  }

  private Request buildWorkflowRequest(ObjectNode payload) throws IOException {
    return new Request.Builder()
        .url(baseUrl + "/workflows/run")
        .addHeader("Authorization", "Bearer " + apiKey)
        .addHeader("Content-Type", "application/json")
        .post(RequestBody.create(SerializationUtils.standardObjectMapper().writeValueAsString(payload), JSON))
        .build();
  }

  private void validateResponse(Response response) {
    if (response == null || !response.isSuccessful()) {
      int statusCode = response != null ? response.code() : -1;
      log.error("DifyApiClient call failed with status code {}", statusCode);
      throw new ImsBusinessException(DIFY_API_CALL_FAILED, statusCode);
    }
  }

  private ObjectNode extractWorkflowResult(Response response) throws IOException {
    if (response.body() == null) {
      throw new ImsBusinessException(DIFY_RESPONSE_BODY_NULL);
    }

    WorkflowStreamProcessor processor = new WorkflowStreamProcessor();
    return processor.processStream(response.body().byteStream());
  }


  public DifyDocumentSegmentQueryResponseDto queryDocumentSegmentsByKeyword(String keyword) throws IOException {
    Request request = buildQueryDocumentSegmentationRequest(keyword);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      if (response.body() == null) {
        throw new ImsBusinessException(DIFY_RESPONSE_BODY_NULL);
      }
      String responseBody = response.body().string();
      return SerializationUtils.standardObjectMapper().readValue(responseBody, DifyDocumentSegmentQueryResponseDto.class);
    }
  }


  public DifyDocumentSegmentUpdateResponseDto updateDocumentSegment(String segmentId, DifyDocumentSegmentUpdateRequestBodyDto updateRequest) throws IOException {
    Request request = buildUpdateDocumentSegmentRequest(segmentId, updateRequest);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      if (response.body() == null) {
        throw new ImsBusinessException(DIFY_RESPONSE_BODY_NULL);
      }
      String responseBody = response.body().string();
      return SerializationUtils.standardObjectMapper().readValue(responseBody, DifyDocumentSegmentUpdateResponseDto.class);
    }
  }


  public DifyDocumentSegmentQueryResponseDto createDocumentSegment(DifyDocumentSegmentCreateRequestBodyDto createRequest) throws IOException {
    Request request = buildCreateDocumentSegmentRequest(createRequest);

    try (Response response = client.execute(request)) {
      validateResponse(response);
      if (response.body() == null) {
        throw new ImsBusinessException(DIFY_RESPONSE_BODY_NULL);
      }
      String responseBody = response.body().string();
      return SerializationUtils.standardObjectMapper().readValue(responseBody, DifyDocumentSegmentQueryResponseDto.class);
    }
  }


  private Request buildQueryDocumentSegmentationRequest(String keyword) {

    HttpUrl url = Objects.requireNonNull(HttpUrl.parse(baseUrl))
            .newBuilder()
            .addPathSegments("datasets/" + datasetId + "/documents/" + documentId + "/segments")
            .addQueryParameter("keyword", keyword)
            .build();

    return new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + knowledgeBaseApiKey)
            .addHeader("Content-Type", "application/json")
            .get()
            .build();
  }

  private Request buildUpdateDocumentSegmentRequest(String segmentId, DifyDocumentSegmentUpdateRequestBodyDto updateRequest) throws IOException {
    HttpUrl url = Objects.requireNonNull(HttpUrl.parse(baseUrl))
            .newBuilder()
            .addPathSegments("datasets/" + datasetId + "/documents/" + documentId + "/segments/" + segmentId)
            .build();

    String requestBody = SerializationUtils.standardObjectMapper().writeValueAsString(updateRequest);

    return new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + knowledgeBaseApiKey)
            .addHeader("Content-Type", "application/json")
            .post(RequestBody.create(requestBody, JSON))
            .build();
  }

  private Request buildCreateDocumentSegmentRequest(DifyDocumentSegmentCreateRequestBodyDto createRequest) throws IOException {
    HttpUrl url = Objects.requireNonNull(HttpUrl.parse(baseUrl))
            .newBuilder()
            .addPathSegments("datasets/" + datasetId + "/documents/" + documentId + "/segments")
            .build();

    String requestBody = SerializationUtils.standardObjectMapper().writeValueAsString(createRequest);

    log.info("[buildCreateDocumentSegmentRequest] requestBody: {}", requestBody);

    return new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + knowledgeBaseApiKey)
            .addHeader("Content-Type", "application/json")
            .post(RequestBody.create(requestBody, JSON))
            .build();
  }
}
