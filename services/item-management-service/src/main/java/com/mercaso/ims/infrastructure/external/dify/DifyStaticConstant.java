package com.mercaso.ims.infrastructure.external.dify;

import com.fasterxml.jackson.databind.ObjectMapper;

public class DifyStaticConstant {

    private DifyStaticConstant() {
        throw new IllegalStateException("Constant class");
    }

    protected static final String WORKFLOW_RUN_ID = "workflow_run_id";
    protected static final String DATA_PREFIX = "data: ";
    protected static final String DONE_MARKER = "[DONE]";
    protected static  final String OUTPUT = "output";
}
