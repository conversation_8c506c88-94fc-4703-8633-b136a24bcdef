package com.mercaso.ims.infrastructure.external.dify.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DifyDocumentSegmentQueryResponseDto {
    
    private List<DifyDocumentSegmentDto> data;
    
    @JsonProperty("doc_form")
    private String docForm;
    
    private Integer total;
    
    @JsonProperty("has_more")
    private Boolean hasMore;
    
    private Integer limit;
    
    private Integer page;
}
