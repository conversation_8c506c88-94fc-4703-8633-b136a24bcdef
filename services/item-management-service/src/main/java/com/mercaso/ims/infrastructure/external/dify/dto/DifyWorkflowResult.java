package com.mercaso.ims.infrastructure.external.dify.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DifyWorkflowResult {
    @JsonProperty("workflow_run_id")
    private String workflowRunId;

    @JsonProperty("total_tokens")
    private Long totalTokens;

    @JsonProperty("total_steps")
    private Integer totalSteps;

    @JsonProperty("elapsed_time")
    private Double elapsedTime;

    @JsonProperty("output")
    private String result;

    @JsonProperty("status")
    private String status;
}
