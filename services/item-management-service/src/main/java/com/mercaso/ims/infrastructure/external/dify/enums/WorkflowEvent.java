package com.mercaso.ims.infrastructure.external.dify.enums;

import lombok.Getter;

import java.util.Optional;

@Getter
public enum WorkflowEvent {
    WORKFLOW_STARTED("workflow_started"),
    WORKFLOW_FINISHED("workflow_finished"),
    WOR<PERSON><PERSON>OW_SUCCEEDED("workflow_succeeded"),
    WOR<PERSON><PERSON>OW_FAILED("workflow_failed"),
    ERROR("error");

    private final String eventName;

    WorkflowEvent(String eventName) {
        this.eventName = eventName;
    }

    public static Optional<WorkflowEvent> fromString(String eventName) {
        for (WorkflowEvent event : values()) {
            if (event.eventName.equals(eventName)) {
                return Optional.of(event);
            }
        }
        return Optional.empty();
    }
}
