package com.mercaso.ims.infrastructure.process.matcher;

import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;

import com.google.api.client.util.Lists;
import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.dto.VendorItemMappingDto;
import com.mercaso.ims.domain.itemcostchangerequest.enums.CostType;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.external.downey.DowneyAdaptor;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class DowneyVendorItemMatcher implements VendorItemMatcher {

    private final VendorItemService vendorItemService;
    private final VendorService vendorService;
    private final DowneyAdaptor downeyAdaptor;


    @Override
    public List<VendorItemMappingDto> matchItem(ItemCostCollectionItemParsingResultDto invoiceItem, UUID vendorId) {
        List<VendorItemMappingDto> vendorItemMappings = Lists.newArrayList();
        Vendor vendor = vendorService.findByVendorName(DOWNEY_WHOLESALE);

        String vendorItemNumber = invoiceItem.getVendorSkuNumber();
        List<VendorItem> vendorItems = null;
        if (StringUtils.isNotBlank(vendorItemNumber)) {
            vendorItems = vendorItemService.findByVendorIDAndVendorSkuNum(vendor.getId(),
                vendorItemNumber);
        }
        if (vendorItems != null && !vendorItems.isEmpty()) {
            vendorItems.forEach(vendorItem -> vendorItemMappings.add(createVendorItemMapping(vendor,
                vendorItem,
                invoiceItem)));
        } else {
            VendorItemMappingDto vendorItemMappingDto = new VendorItemMappingDto();
            vendorItemMappingDto.setVendorId(vendor.getId());
            vendorItemMappingDto.setMatchedType(MatchedType.MISS_MATCHED);
            vendorItemMappingDto.setVendorName(DOWNEY_WHOLESALE);
            vendorItemMappingDto.setCostType(getSupportedCostType());

            vendorItemMappings.add(vendorItemMappingDto);
        }
        return vendorItemMappings;
    }


    @Override
    public boolean isSupported(String vendorName, ItemCostCollectionSources sources) {
        return VendorConstant.DOWNEY_WHOLESALE.equals(vendorName)
            && !sources.equals(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
    }

    private VendorItemMappingDto createVendorItemMapping(Vendor vendor, VendorItem vendorItem,
        ItemCostCollectionItemParsingResultDto invoiceItem) {
        VendorItemMappingDto dto = new VendorItemMappingDto();
        dto.setVendorId(vendor.getId());
        dto.setItemId(vendorItem.getItemId());
        dto.setVendorItemId(vendorItem.getId());
        dto.setVendorName(DOWNEY_WHOLESALE);

        dto.setPreviousCost(vendorItem.getBackupPackPlusCrvCost());

        dto.setMatchedType(isAutoMatchedAndUpdated(vendorItem.getBackupPackPlusCrvCost(), invoiceItem.getCost()) ?
            MatchedType.AUTO_MATCHED_AND_UPDATED :
            MatchedType.MATCHED);
        dto.setCostType(getSupportedCostType());

        return dto;
    }

    private boolean isAutoMatchedAndUpdated(BigDecimal currentCost, BigDecimal invoiceCost) {
        return currentCost != null && currentCost.compareTo(invoiceCost) == 0;
    }

    @Override
    public String getSupportedCostType() {
        return CostType.JIT_COST.getCostTypeName();
    }

}
