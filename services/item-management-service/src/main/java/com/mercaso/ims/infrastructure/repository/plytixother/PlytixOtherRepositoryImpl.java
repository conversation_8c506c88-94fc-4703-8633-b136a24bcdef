package com.mercaso.ims.infrastructure.repository.plytixother;

import com.mercaso.ims.domain.plytixother.PlytixOtherData;
import com.mercaso.ims.domain.plytixother.PlytixOtherRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.plytixother.jpa.PlytixOtherDataJpaDao;
import com.mercaso.ims.infrastructure.repository.plytixother.jpa.dataobject.PlytixOtherDataDo;
import com.mercaso.ims.infrastructure.repository.plytixother.jpa.mapper.PlytixOtherDataDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PlytixOtherRepositoryImpl implements PlytixOtherRepository {

    private final PlytixOtherDataJpaDao plytixOtherDataJpaDao;

    private final PlytixOtherDataDoMapper plytixOtherDataDoMapper;

    @Override
    public PlytixOtherData save(PlytixOtherData domain) {
        PlytixOtherDataDo plytixOtherDataDo = plytixOtherDataDoMapper.domainToDo(domain);
        plytixOtherDataDo = plytixOtherDataJpaDao.save(plytixOtherDataDo);
        return plytixOtherDataDoMapper.doToDomain(plytixOtherDataDo);
    }

    @Override
    public PlytixOtherData findById(UUID id) {
        PlytixOtherDataDo plytixOtherDataDo = plytixOtherDataJpaDao.findById(id).orElse(null);
        return plytixOtherDataDoMapper.doToDomain(plytixOtherDataDo);
    }

    @Override
    public PlytixOtherData update(PlytixOtherData domain) {
        PlytixOtherDataDo plytixOtherDataDo = plytixOtherDataJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(plytixOtherDataDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.PLYTIX_OTHER_DATA_NOT_FOUND.getCode());
        }
        PlytixOtherDataDo plytixOtherDataDoTarget = plytixOtherDataDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(plytixOtherDataDoTarget, plytixOtherDataDo, ignoreProperties.toArray(new String[0]));
        PlytixOtherDataDo result = plytixOtherDataJpaDao.save(plytixOtherDataDo);
        return plytixOtherDataDoMapper.doToDomain(result);
    }

    @Override
    public PlytixOtherData deleteById(UUID id) {
        PlytixOtherDataDo plytixOtherDataDo = plytixOtherDataJpaDao.findById(id).orElse(null);
        if (null == plytixOtherDataDo) {
            return null;
        }
        plytixOtherDataDo.setDeletedAt(Instant.now());
        plytixOtherDataDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return plytixOtherDataDoMapper.doToDomain(plytixOtherDataJpaDao.save(plytixOtherDataDo));
    }

    @Override
    public PlytixOtherData findByItemId(UUID itemId) {
        PlytixOtherDataDo plytixOtherDataDo = plytixOtherDataJpaDao.findByItemId(itemId);
        if (null == plytixOtherDataDo) {
            return null;
        }
        return plytixOtherDataDoMapper.doToDomain(plytixOtherDataDo);
    }

    @Override
    public List<PlytixOtherData> findByItemIds(List<UUID> itemIds) {
        return Optional.ofNullable(plytixOtherDataJpaDao.findByItemIdIn(itemIds))
            .orElse(Collections.emptyList())
            .stream()
            .map(plytixOtherDataDoMapper::doToDomain)
            .toList().reversed();
    }
}
