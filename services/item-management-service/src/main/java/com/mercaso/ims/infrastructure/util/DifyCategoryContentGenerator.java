package com.mercaso.ims.infrastructure.util;

import com.mercaso.ims.application.dto.CategoryGroupDto;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.OPEN_AI_ERROR;


@Component
@RequiredArgsConstructor
@Slf4j
public class DifyCategoryContentGenerator {

    private final ChatClient chatClient;

    private static final  String ID_PREFIX = " (ID: ";

    private static final  String ID_SUFFIX = ")";

    public String generateCategoryContent(CategoryGroupDto categoryGroup) {
        if (categoryGroup == null) {
            return "";
        }

        try {
            String categoryPath = buildCategoryPath(categoryGroup);
            String categoryBreakdown = buildCategoryBreakdownWithAI(categoryGroup);

            if (!StringUtils.hasText(categoryBreakdown)) {
                return "";
            }
            
            return buildPrompt(categoryPath, categoryBreakdown);
        } catch (Exception e) {
            log.error("Failed to generate content using OpenAI, falling back to static content", e);
            throw new ImsBusinessException(OPEN_AI_ERROR);
        }
    }

    public List<String> generateKeywords(String categoryContent) {
        if (!StringUtils.hasText(categoryContent)) {
            return new ArrayList<>();
        }


        try {
            List<String> aiKeywords = generateKeywordsWithAI(categoryContent);

            List<String> allKeywords = new ArrayList<>(aiKeywords);

            return allKeywords.stream().distinct().toList();

        } catch (Exception e) {
            log.error("Failed to generate keywords using AI, falling back to static keywords", e);
            throw new ImsBusinessException(OPEN_AI_ERROR);
        }
    }

    private List<String> generateKeywordsWithAI(String categoryContent) {
        log.info("Generate keywords from category content using AI...");

        try {
            String result = chatClient.prompt()
                .system("You are an expert in e-commerce keyword extraction. Your job is to extract relevant keywords from product category descriptions for search and indexing purposes.")
                .user(String.format(
                        """
                            Please extract 10-15 relevant keywords from the following e-commerce category content.
                            Focus on product types, features, use cases, and industry terms that would be useful for search.
                            Return only the keywords separated by commas, without explanations.
    
                            Category Content:
                            %s
                        """,
                    categoryContent
                ))
                .call()
                .content();

            log.info("AI-generated keywords: {}", result);

            return StringUtils.hasText(result) ? Arrays.stream(result.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .toList() : List.of();

        } catch (Exception e) {
            log.error("Error generating keywords with AI: {}", e.getMessage());
            return new ArrayList<>();
        }
    }


    private String buildCategoryBreakdownWithAI(CategoryGroupDto categoryGroup) {
        StringBuilder breakdown = new StringBuilder();
        String[] levels = {"department", "category", "sub-category", "clazz"};
        String[] names = {
            categoryGroup.getDepartmentName(),
            categoryGroup.getCategoryName(), 
            categoryGroup.getSubCategoryName(),
            categoryGroup.getClazzName()
        };

        for (int i = 0; i < levels.length; i++) {
            if (StringUtils.hasText(names[i])) {
                String description = getCategoryDescription(names[i], levels[i]);
                breakdown.append("- **").append(names[i]).append("**: ").append(description).append("\n");
                
                try {
                    Thread.sleep(1100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Thread interrupted during rate limiting delay", e);
                }
            }
        }
        
        return breakdown.toString();
    }


    private String getCategoryDescription(String categoryName, String level) {
        log.info("Get the description for each category level，category_name: {}, level: {}", categoryName, level);

        try {
            String result = chatClient.prompt()
                .system("You are an expert in e-commerce taxonomy. Your job is to help describe the function and scope of each level in a category path.")
                .user(String.format(
                    "Please provide a clear, concise description (1-2 sentences) of the following e-commerce category used in B2B platforms: **%s**. " +
                    "Focus on describing the type of products it typically includes and its role in the overall category structure. " +
                    "Avoid vague language. This category appears at the **%s** level of a 4-level hierarchy.",
                    categoryName, level
                ))
                .call()
                .content();
                
            log.info("Get OpenAI results，response: {}", result);
            return StringUtils.hasText(result) ? result.strip() : "";
            
        } catch (Exception e) {
            log.error("Error fetching description for {}: {}", categoryName, e.getMessage());
            return "";
        }
    }


    private String buildPrompt(String categoryPath, String categoryBreakdown) {
        return String.format("""
            Product classification task:
            Given the hierarchical e-commerce category path:
            %s,
            identify the most suitable classification for a related product.

            Category breakdown:
            %s

            What kind of product typically falls into this path?
            Please carefully consider the context of **B2B retail environments**, **shopping behavior**, and **customer expectations** when browsing online.
            Provide the most **granular and relevant** classification possible based on usage scenario and product function.""",
            categoryPath, categoryBreakdown);
    }

    private String buildCategoryPath(CategoryGroupDto categoryGroup) {
        List<String> pathParts = new ArrayList<>();
        
        if (StringUtils.hasText(categoryGroup.getDepartmentName()) && categoryGroup.getDepartmentId() != null) {
            pathParts.add(categoryGroup.getDepartmentName() + ID_PREFIX + categoryGroup.getDepartmentId() + ID_SUFFIX);
        }
        
        if (StringUtils.hasText(categoryGroup.getCategoryName()) && categoryGroup.getCategoryId() != null) {
            pathParts.add(categoryGroup.getCategoryName() + ID_PREFIX + categoryGroup.getCategoryId() + ID_SUFFIX);
        }
        
        if (StringUtils.hasText(categoryGroup.getSubCategoryName()) && categoryGroup.getSubCategoryId() != null) {
            pathParts.add(categoryGroup.getSubCategoryName() + ID_PREFIX + categoryGroup.getSubCategoryId() + ID_SUFFIX);
        }
        
        if (StringUtils.hasText(categoryGroup.getClazzName()) && categoryGroup.getClazzId() != null) {
            pathParts.add(categoryGroup.getClazzName() + ID_PREFIX + categoryGroup.getClazzId() + ID_SUFFIX);
        }
        
        return String.join(" > ", pathParts);
    }
}
