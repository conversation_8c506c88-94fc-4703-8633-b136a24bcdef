package com.mercaso.ims.infrastructure.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

public class FormatUtils {

    public static final String DEFAULT_PATTERN = "MM/dd/yyyy HH:mm";
    public static final String ZONE_PATTERN = "MM/dd/yy HH:mm zzz";

    private FormatUtils() {
    }

    public static String currencyFormat(BigDecimal d) {
        return d != null ? coseFormat(d.doubleValue(), "$") : null;
    }

    public static String currencyWithSymbolFormat(BigDecimal d, String symbol) {
        return d != null ? coseFormat(d.doubleValue(), symbol) : null;
    }

    private static String coseFormat(Double d, String symbol) {
        Double tmp = d;
        String result = null;
        if (d < 0) {
            tmp = Math.abs(d);
            result = "-" + symbol + numberFormat(tmp);
        } else {
            result = symbol + numberFormat(tmp);
        }

        if (!result.contains(".")) {
            result = result.concat(".00");
        }
        return result;
    }

    public static String numberFormat(BigDecimal bigDecimal) {
        return bigDecimal != null ? numberFormat(bigDecimal.doubleValue()) : null;
    }

    public static String numberFormat(Double d) {
        if (d == null) {
            return "0.00";
        }
        DecimalFormat df = new DecimalFormat("#,###.00");
        String str = df.format(d);
        if (str.startsWith(".")) {
            str = "0" + str;
        } else if (str.startsWith("-.")) {
            str = "-0." + str.substring(str.indexOf('.') + 1);
        }
        return str;
    }

    public static String instantToStringWithFormat(Instant instant, String format) {
        if (instant == null) {
            return null;
        }
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(format).withZone(ZoneId.systemDefault());

        return dateFormat.format(instant);
    }

    /*
     * Example: 'HOOK CHASSIS' to 'Hook Chassis'
     */
    public static String convertToUpperCamel(String str) {
        StringBuffer stringer = new StringBuffer();
        Matcher m = Pattern.compile("([a-z])([a-z]*)", Pattern.CASE_INSENSITIVE).matcher(str);

        while (m.find()) {
            m.appendReplacement(stringer, m.group(1).toUpperCase() + m.group(2).toLowerCase());
        }
        return m.appendTail(stringer).toString();
    }

    public static boolean isLongString(String str) {
        try {
            Long.parseLong(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean isImageUrl(String urlStr) {
        String[] imageExtensions = {"jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp"};

        try {
            URL url = new URL(urlStr);
            String filePath = url.getPath();
            int lastIndexOfDot = filePath.lastIndexOf('.');
            if (lastIndexOfDot > 0 && lastIndexOfDot < filePath.length() - 1) {
                String extension = filePath.substring(lastIndexOfDot + 1).toLowerCase();
                for (String ext : imageExtensions) {
                    if (extension.startsWith(ext)) {
                        return true;
                    }
                }
            }
        } catch (MalformedURLException e) {
            return false;
        }
        return false;
    }

    public static String cleanInput(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }
        return input.replaceAll("[^\\p{Print}]", "").trim();
    }


    public static String toPercentageString(BigDecimal value) {
        BigDecimal percentageValue = value.multiply(new BigDecimal("100"));

        BigDecimal roundedPercentageValue = percentageValue.setScale(2, RoundingMode.HALF_UP);

        return roundedPercentageValue + "%";
    }

    public static String toPriceFormat(BigDecimal price) {
        DecimalFormat currencyFormat = (DecimalFormat) NumberFormat.getCurrencyInstance(Locale.US);
        currencyFormat.applyPattern("¤#,##0.00");
        return currencyFormat.format(price);
    }

    public static String toBoldString(String value) {
        return String.format("*%s*", value);
    }

}
