package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.service.BulkExportApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/bulk-export")
@RequiredArgsConstructor
public class BulkExportRestApi {

    private final BulkExportApplicationService bulkExportApplicationService;

    @GetMapping("/download")
    @PreAuthorize("hasAuthority('ims:read:merchandise-reports')")
    public void downloadFilteredBulkReport(@RequestParam(value = "customFilter", required = false) String customFilter,
                                           @RequestParam(value = "exportType", required = false, defaultValue = "ITEM") ExportType exportType) {
        bulkExportApplicationService.getBulkReportFile(customFilter, exportType);
    }

}

