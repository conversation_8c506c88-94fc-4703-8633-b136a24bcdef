package com.mercaso.ims.application.service.impl;

import com.mercaso.ims.application.dto.CategoryGroupDto;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.infrastructure.external.dify.DifyApiClient;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentCreateRequestBodyDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentQueryResponseDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentUpdateRequestBodyDto;
import com.mercaso.ims.infrastructure.util.DifyCategoryContentGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DifyKnowledgeBaseApplicationServiceImplTest {

    @Mock
    private DifyApiClient difyApiClient;

    @Mock
    private CategoryApplicationService categoryApplicationService;

    @Mock
    private DifyCategoryContentGenerator difyCategoryContentGenerator;

    @InjectMocks
    private DifyKnowledgeBaseApplicationServiceImpl difyKnowledgeBaseApplicationService;

    private UUID testCategoryId;
    private CategoryGroupDto testCategoryGroup;
    private DifyDocumentSegmentQueryResponseDto testQueryResponse;
    private DifyDocumentSegmentDto testSegment;

    @BeforeEach
    void setUp() {
        testCategoryId = UUID.randomUUID();
        
        testCategoryGroup = CategoryGroupDto.builder()
            .clazzId(testCategoryId)
            .clazzName("Test Class")
            .subCategoryName("Test SubCategory")
            .categoryName("Test Category")
            .departmentName("Test Department")
            .build();

        testSegment = DifyDocumentSegmentDto.builder()
            .id("test-segment-id")
            .content("existing content")
            .keywords(List.of("existing", "keywords"))
            .enabled(true)
            .build();

        testQueryResponse = DifyDocumentSegmentQueryResponseDto.builder()
            .data(List.of(testSegment))
            .total(1)
            .build();
    }

    // Tests for syncKnowledgeBaseByCategory method
    @Test
    void syncKnowledgeBaseByCategory_WithExistingSegments_UpdatesSegments() throws IOException {
        // Arrange
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(testQueryResponse);
        when(difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup))
            .thenReturn("generated content");
        when(difyCategoryContentGenerator.generateKeywords("generated content"))
            .thenReturn(List.of("generated", "keywords"));

        // Act
        difyKnowledgeBaseApplicationService.syncKnowledgeBaseByCategory(testCategoryId);

        // Assert
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(testCategoryId);
        verify(difyApiClient).queryDocumentSegmentsByKeyword(testCategoryId.toString());
        verify(difyCategoryContentGenerator).generateCategoryContent(testCategoryGroup);
        verify(difyCategoryContentGenerator).generateKeywords("generated content");
        verify(difyApiClient).updateDocumentSegment(eq("test-segment-id"), any(DifyDocumentSegmentUpdateRequestBodyDto.class));
        verify(difyApiClient, never()).createDocumentSegment(any());
    }

    @Test
    void syncKnowledgeBaseByCategory_WithNoExistingSegments_CreatesNewSegment() throws IOException {
        // Arrange
        DifyDocumentSegmentQueryResponseDto emptyResponse = DifyDocumentSegmentQueryResponseDto.builder()
            .data(Collections.emptyList())
            .total(0)
            .build();

        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(emptyResponse);
        when(difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup))
            .thenReturn("generated content");
        when(difyCategoryContentGenerator.generateKeywords("generated content"))
            .thenReturn(List.of("generated", "keywords"));

        // Act
        difyKnowledgeBaseApplicationService.syncKnowledgeBaseByCategory(testCategoryId);

        // Assert
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(testCategoryId);
        verify(difyApiClient).queryDocumentSegmentsByKeyword(testCategoryId.toString());
        verify(difyCategoryContentGenerator).generateCategoryContent(testCategoryGroup);
        verify(difyCategoryContentGenerator).generateKeywords("generated content");
        verify(difyApiClient).createDocumentSegment(any(DifyDocumentSegmentCreateRequestBodyDto.class));
        verify(difyApiClient, never()).updateDocumentSegment(anyString(), any());
    }

    @Test
    void syncKnowledgeBaseByCategory_WithNullQueryResponse_CreatesNewSegment() throws IOException {
        // Arrange
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(null);
        when(difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup))
            .thenReturn("generated content");
        when(difyCategoryContentGenerator.generateKeywords("generated content"))
            .thenReturn(List.of("generated", "keywords"));

        // Act
        difyKnowledgeBaseApplicationService.syncKnowledgeBaseByCategory(testCategoryId);

        // Assert
        verify(difyApiClient).createDocumentSegment(any(DifyDocumentSegmentCreateRequestBodyDto.class));
        verify(difyApiClient, never()).updateDocumentSegment(anyString(), any());
    }

    @Test
    void syncKnowledgeBaseByCategory_WithCategoryNotFound_ReturnsEarly() throws IOException {
        // Arrange
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(null);

        // Act
        difyKnowledgeBaseApplicationService.syncKnowledgeBaseByCategory(testCategoryId);

        // Assert
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(testCategoryId);
        verify(difyApiClient, never()).queryDocumentSegmentsByKeyword(anyString());
        verify(difyApiClient, never()).updateDocumentSegment(anyString(), any());
        verify(difyApiClient, never()).createDocumentSegment(any());
    }

    @Test
    void syncKnowledgeBaseByCategory_WithEmptyContent_SkipsUpdate() throws IOException {
        // Arrange
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(testQueryResponse);
        when(difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup))
            .thenReturn(""); // Empty content

        // Act
        difyKnowledgeBaseApplicationService.syncKnowledgeBaseByCategory(testCategoryId);

        // Assert
        verify(difyCategoryContentGenerator).generateCategoryContent(testCategoryGroup);
        verify(difyApiClient, never()).updateDocumentSegment(anyString(), any());
    }

    @Test
    void syncKnowledgeBaseByCategory_WithIOException_PropagatesException() throws IOException {
        // Arrange
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenThrow(new IOException("Network error"));

        // Act & Assert
        IOException exception = assertThrows(IOException.class,
            () -> difyKnowledgeBaseApplicationService.syncKnowledgeBaseByCategory(testCategoryId));

        assertEquals("Network error", exception.getMessage());
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(testCategoryId);
        verify(difyApiClient).queryDocumentSegmentsByKeyword(testCategoryId.toString());
    }

    // Tests for queryDocumentSegmentsByKeyword method
    @Test
    void queryDocumentSegmentsByKeyword_Success_ReturnsResponse() throws IOException {
        // Arrange
        String keyword = "test-keyword";
        when(difyApiClient.queryDocumentSegmentsByKeyword(keyword))
            .thenReturn(testQueryResponse);

        // Act
        DifyDocumentSegmentQueryResponseDto result = 
            difyKnowledgeBaseApplicationService.queryDocumentSegmentsByKeyword(keyword);

        // Assert
        assertNotNull(result);
        assertEquals(testQueryResponse, result);
        verify(difyApiClient).queryDocumentSegmentsByKeyword(keyword);
    }

    @Test
    void queryDocumentSegmentsByKeyword_WithIOException_PropagatesException() throws IOException {
        // Arrange
        String keyword = "test-keyword";
        when(difyApiClient.queryDocumentSegmentsByKeyword(keyword))
            .thenThrow(new IOException("Network error"));

        // Act & Assert
        IOException exception = assertThrows(IOException.class,
            () -> difyKnowledgeBaseApplicationService.queryDocumentSegmentsByKeyword(keyword));

        assertEquals("Network error", exception.getMessage());
        verify(difyApiClient).queryDocumentSegmentsByKeyword(keyword);
    }

    // Tests for deletedKnowledgeBaseSegmentsByCategory method
    @Test
    void deletedKnowledgeBaseSegmentsByCategory_WithExistingSegments_UpdatesSegmentsAsDisabled() throws IOException {
        // Arrange
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(testQueryResponse);

        // Act
        difyKnowledgeBaseApplicationService.deletedKnowledgeBaseSegmentsByCategory(testCategoryId);

        // Assert
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(testCategoryId);
        verify(difyApiClient).queryDocumentSegmentsByKeyword(testCategoryId.toString());
        verify(difyApiClient).updateDocumentSegment(eq("test-segment-id"), any(DifyDocumentSegmentUpdateRequestBodyDto.class));

        // Verify that content generator is not called for deletion (enabled=false)
        verify(difyCategoryContentGenerator, never()).generateCategoryContent(any());
        verify(difyCategoryContentGenerator, never()).generateKeywords(anyString());
    }

    @Test
    void deletedKnowledgeBaseSegmentsByCategory_WithNoExistingSegments_DoesNothing() throws IOException {
        // Arrange
        DifyDocumentSegmentQueryResponseDto emptyResponse = DifyDocumentSegmentQueryResponseDto.builder()
            .data(Collections.emptyList())
            .total(0)
            .build();

        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(emptyResponse);

        // Act
        difyKnowledgeBaseApplicationService.deletedKnowledgeBaseSegmentsByCategory(testCategoryId);

        // Assert
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(testCategoryId);
        verify(difyApiClient).queryDocumentSegmentsByKeyword(testCategoryId.toString());
        verify(difyApiClient, never()).updateDocumentSegment(anyString(), any());
    }

    @Test
    void deletedKnowledgeBaseSegmentsByCategory_WithNullQueryResponse_DoesNothing() throws IOException {
        // Arrange
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(null);

        // Act
        difyKnowledgeBaseApplicationService.deletedKnowledgeBaseSegmentsByCategory(testCategoryId);

        // Assert
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(testCategoryId);
        verify(difyApiClient).queryDocumentSegmentsByKeyword(testCategoryId.toString());
        verify(difyApiClient, never()).updateDocumentSegment(anyString(), any());
    }

    @Test
    void deletedKnowledgeBaseSegmentsByCategory_WithIOException_PropagatesException() throws IOException {
        // Arrange
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenThrow(new IOException("Network error"));

        // Act & Assert
        IOException exception = assertThrows(IOException.class,
            () -> difyKnowledgeBaseApplicationService.deletedKnowledgeBaseSegmentsByCategory(testCategoryId));

        assertEquals("Network error", exception.getMessage());
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(testCategoryId);
        verify(difyApiClient).queryDocumentSegmentsByKeyword(testCategoryId.toString());
    }

    // Tests for private method updateExistingSegments (tested through public methods)
    @Test
    void updateExistingSegments_WithMultipleSegments_UpdatesAllSegments() throws IOException {
        // Arrange
        DifyDocumentSegmentDto segment2 = DifyDocumentSegmentDto.builder()
            .id("test-segment-id-2")
            .content("existing content 2")
            .keywords(List.of("existing2", "keywords2"))
            .enabled(true)
            .build();

        DifyDocumentSegmentQueryResponseDto multiSegmentResponse = DifyDocumentSegmentQueryResponseDto.builder()
            .data(List.of(testSegment, segment2))
            .total(2)
            .build();

        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(multiSegmentResponse);
        when(difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup))
            .thenReturn("generated content");
        when(difyCategoryContentGenerator.generateKeywords("generated content"))
            .thenReturn(List.of("generated", "keywords"));

        // Act
        difyKnowledgeBaseApplicationService.syncKnowledgeBaseByCategory(testCategoryId);

        // Assert
        verify(difyApiClient).updateDocumentSegment(eq("test-segment-id"), any(DifyDocumentSegmentUpdateRequestBodyDto.class));
        verify(difyApiClient).updateDocumentSegment(eq("test-segment-id-2"), any(DifyDocumentSegmentUpdateRequestBodyDto.class));
        verify(difyApiClient, never()).createDocumentSegment(any());
    }

    // Tests for private method createNewSegment (tested through public methods)
    @Test
    void createNewSegment_WithEmptyContent_DoesNotCreateSegment() throws IOException {
        // Arrange
        DifyDocumentSegmentQueryResponseDto emptyResponse = DifyDocumentSegmentQueryResponseDto.builder()
            .data(Collections.emptyList())
            .total(0)
            .build();

        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(testCategoryId))
            .thenReturn(testCategoryGroup);
        when(difyApiClient.queryDocumentSegmentsByKeyword(testCategoryId.toString()))
            .thenReturn(emptyResponse);
        when(difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup))
            .thenReturn(""); // Empty content

        // Act
        difyKnowledgeBaseApplicationService.syncKnowledgeBaseByCategory(testCategoryId);

        // Assert
        verify(difyCategoryContentGenerator).generateCategoryContent(testCategoryGroup);
        verify(difyApiClient, never()).createDocumentSegment(any());
    }
}
