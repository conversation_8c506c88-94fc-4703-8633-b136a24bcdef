package com.mercaso.ims.infrastructure.external.dify;

import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentCreateRequestBodyDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentCreateRequestDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentQueryResponseDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentUpdateRequestBodyDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentUpdateRequestDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyDocumentSegmentUpdateResponseDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyWorkflowResult;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.List;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.DIFY_API_CALL_FAILED;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.DIFY_RESPONSE_BODY_NULL;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DifyApiClientTest {

    @Mock
    private HttpClient httpClient;

    @Mock
    private Response response;

    @Mock
    private ResponseBody responseBody;

    @InjectMocks
    private DifyApiClient difyApiClient;

    private static final String TEST_API_KEY = "test-api-key";
    private static final String TEST_BASE_URL = "https://api.dify.ai";
    private static final String TEST_KEY = "test-key";
    private static final String TEST_VALUE = "test-value";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(difyApiClient, "apiKey", TEST_API_KEY);
        ReflectionTestUtils.setField(difyApiClient, "baseUrl", TEST_BASE_URL);
    }

    @Test
    void callDifyWorkflow_Success() throws IOException {
        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Arrange
            securityUtilMock.when(SecurityUtil::getUserName).thenReturn("test-user");

            String mockStreamData = """
                data: {"event": "workflow_started", "workflow_run_id": "test-workflow-id"}

                data: {"event": "workflow_succeeded", "data": {"total_tokens": 100, "total_steps": 5, "elapsed_time": 2.5, "outputs": {"result": "success"}}}

                data: [DONE]
                """;

            when(httpClient.execute(any(Request.class))).thenReturn(response);
            when(response.isSuccessful()).thenReturn(true);
            when(response.body()).thenReturn(responseBody);
            when(responseBody.byteStream()).thenReturn(new ByteArrayInputStream(mockStreamData.getBytes()));

            // Act
            DifyWorkflowResult result = difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE);

            // Assert
            assertNotNull(result);
            assertEquals("test-workflow-id", result.getWorkflowRunId());
            assertEquals(100L, result.getTotalTokens());
            assertEquals(5, result.getTotalSteps());
            assertEquals(2.5, result.getElapsedTime());
            assertEquals("succeeded", result.getStatus());

            verify(httpClient).execute(any(Request.class));
            verify(response).isSuccessful();
            verify(response, atLeastOnce()).body(); // Allow multiple calls
            verify(responseBody).byteStream();
        }
    }

    @Test
    void callDifyWorkflow_ResponseNotSuccessful_ThrowsException() throws IOException {
        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Arrange
            securityUtilMock.when(SecurityUtil::getUserName).thenReturn("test-user");

            when(httpClient.execute(any(Request.class))).thenReturn(response);
            when(response.isSuccessful()).thenReturn(false);
            when(response.code()).thenReturn(400);

            // Act & Assert
            ImsBusinessException exception = assertThrows(ImsBusinessException.class,
                () -> difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE));

            assertNotNull(exception);
            if (exception.getMessage() != null) {
                assertTrue(exception.getCode().contains(DIFY_API_CALL_FAILED.getCode()));
            }
            verify(httpClient).execute(any(Request.class));
            verify(response).isSuccessful();
            verify(response).code();
        }
    }

    @Test
    void callDifyWorkflow_NullResponse_ThrowsException() throws IOException {
        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Arrange
            securityUtilMock.when(SecurityUtil::getUserName).thenReturn("test-user");

            when(httpClient.execute(any(Request.class))).thenReturn(null);

            // Act & Assert
            ImsBusinessException exception = assertThrows(ImsBusinessException.class,
                () -> difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE));

            assertNotNull(exception);
            if (exception.getMessage() != null) {
                assertTrue(exception.getCode().contains(DIFY_API_CALL_FAILED.getCode()));
            }
            verify(httpClient).execute(any(Request.class));
        }
    }


    @Test
    void callDifyWorkflow_HttpClientThrowsIOException_PropagatesException() throws IOException {
        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Arrange
            securityUtilMock.when(SecurityUtil::getUserName).thenReturn("test-user");

            when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

            // Act & Assert
            IOException exception = assertThrows(IOException.class,
                () -> difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE));

            assertEquals("Network error", exception.getMessage());
            verify(httpClient).execute(any(Request.class));
        }
    }

    // Tests for queryDocumentSegmentsByKeyword method
    @Test
    void queryDocumentSegmentsByKeyword_Success_ReturnsResponse() throws IOException {
        // Arrange
        String keyword = "test-keyword";
        String responseJson = """
            {
                "data": [
                    {
                        "id": "segment-1",
                        "content": "test content",
                        "keywords": ["test", "keyword"]
                    }
                ],
                "doc_form": "text_model",
                "total": 1,
                "has_more": false,
                "limit": 20,
                "page": 1
            }
            """;

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(responseJson);

        // Act
        DifyDocumentSegmentQueryResponseDto result = difyApiClient.queryDocumentSegmentsByKeyword(keyword);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals("text_model", result.getDocForm());
        assertFalse(result.getHasMore());
        assertEquals(1, result.getData().size());
        assertEquals("segment-1", result.getData().get(0).getId());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryDocumentSegmentsByKeyword_ResponseNotSuccessful_ThrowsException() throws IOException {
        // Arrange
        String keyword = "test-keyword";
        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(404);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyApiClient.queryDocumentSegmentsByKeyword(keyword));

        assertNotNull(exception);
        assertTrue(exception.getCode().contains(DIFY_API_CALL_FAILED.getCode()));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryDocumentSegmentsByKeyword_NullResponseBody_ThrowsException() throws IOException {
        // Arrange
        String keyword = "test-keyword";
        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyApiClient.queryDocumentSegmentsByKeyword(keyword));

        assertNotNull(exception);
        assertTrue(exception.getCode().contains(DIFY_RESPONSE_BODY_NULL.getCode()));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void queryDocumentSegmentsByKeyword_HttpClientThrowsIOException_PropagatesException() throws IOException {
        // Arrange
        String keyword = "test-keyword";
        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        IOException exception = assertThrows(IOException.class,
            () -> difyApiClient.queryDocumentSegmentsByKeyword(keyword));

        assertEquals("Network error", exception.getMessage());
        verify(httpClient).execute(any(Request.class));
    }

    // Tests for updateDocumentSegment method
    @Test
    void updateDocumentSegment_Success_ReturnsResponse() throws IOException {
        // Arrange
        String segmentId = "test-segment-id";
        DifyDocumentSegmentUpdateRequestDto updateRequestDto = DifyDocumentSegmentUpdateRequestDto.builder()
            .content("updated content")
            .answer("updated answer")
            .keywords(List.of("updated", "keywords"))
            .enabled(true)
            .build();
        DifyDocumentSegmentUpdateRequestBodyDto updateRequest = DifyDocumentSegmentUpdateRequestBodyDto.builder()
            .segment(updateRequestDto)
            .build();

        String responseJson = """
            {
                "data": {
                    "id": "test-segment-id",
                    "content": "updated content",
                    "answer": "updated answer",
                    "keywords": ["updated", "keywords"],
                    "enabled": true
                },
                "doc_form": "text_model",
                "total": 1,
                "has_more": false,
                "limit": 20,
                "page": 1
            }
            """;

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(responseJson);

        // Act
        DifyDocumentSegmentUpdateResponseDto result = difyApiClient.updateDocumentSegment(segmentId, updateRequest);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals("text_model", result.getDocForm());
        assertNotNull(result.getData());
        assertEquals("test-segment-id", result.getData().getId());
        assertEquals("updated content", result.getData().getContent());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateDocumentSegment_ResponseNotSuccessful_ThrowsException() throws IOException {
        // Arrange
        String segmentId = "test-segment-id";
        DifyDocumentSegmentUpdateRequestDto updateRequestDto = DifyDocumentSegmentUpdateRequestDto.builder()
            .content("updated content")
            .build();
        DifyDocumentSegmentUpdateRequestBodyDto updateRequest = DifyDocumentSegmentUpdateRequestBodyDto.builder()
            .segment(updateRequestDto)
            .build();

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(400);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyApiClient.updateDocumentSegment(segmentId, updateRequest));

        assertNotNull(exception);
        assertTrue(exception.getCode().contains(DIFY_API_CALL_FAILED.getCode()));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateDocumentSegment_NullResponseBody_ThrowsException() throws IOException {
        // Arrange
        String segmentId = "test-segment-id";
        DifyDocumentSegmentUpdateRequestDto updateRequestDto = DifyDocumentSegmentUpdateRequestDto.builder()
            .content("updated content")
            .build();
        DifyDocumentSegmentUpdateRequestBodyDto updateRequest = DifyDocumentSegmentUpdateRequestBodyDto.builder()
            .segment(updateRequestDto)
            .build();

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyApiClient.updateDocumentSegment(segmentId, updateRequest));

        assertNotNull(exception);
        assertTrue(exception.getCode().contains(DIFY_RESPONSE_BODY_NULL.getCode()));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void updateDocumentSegment_HttpClientThrowsIOException_PropagatesException() throws IOException {
        // Arrange
        String segmentId = "test-segment-id";
        DifyDocumentSegmentUpdateRequestDto updateRequestDto = DifyDocumentSegmentUpdateRequestDto.builder()
            .content("updated content")
            .build();
        DifyDocumentSegmentUpdateRequestBodyDto updateRequest = DifyDocumentSegmentUpdateRequestBodyDto.builder()
            .segment(updateRequestDto)
            .build();

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        IOException exception = assertThrows(IOException.class,
            () -> difyApiClient.updateDocumentSegment(segmentId, updateRequest));

        assertEquals("Network error", exception.getMessage());
        verify(httpClient).execute(any(Request.class));
    }

    // Tests for createDocumentSegment method
    @Test
    void createDocumentSegment_Success_ReturnsResponse() throws IOException {
        // Arrange
        DifyDocumentSegmentCreateRequestDto createRequestDto = DifyDocumentSegmentCreateRequestDto.builder()
            .content("new content")
            .answer("new answer")
            .keywords(List.of("new", "keywords"))
            .enabled(true)
            .build();
        DifyDocumentSegmentCreateRequestBodyDto createRequest = DifyDocumentSegmentCreateRequestBodyDto.builder()
            .segments(List.of(createRequestDto))
            .build();

        String responseJson = """
            {
                "data": [
                    {
                        "id": "new-segment-id",
                        "content": "new content",
                        "answer": "new answer",
                        "keywords": ["new", "keywords"],
                        "enabled": true
                    }
                ],
                "doc_form": "text_model",
                "total": 1,
                "has_more": false,
                "limit": 20,
                "page": 1
            }
            """;

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(responseJson);

        // Act
        DifyDocumentSegmentQueryResponseDto result = difyApiClient.createDocumentSegment(createRequest);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals("text_model", result.getDocForm());
        assertFalse(result.getHasMore());
        assertEquals(1, result.getData().size());
        assertEquals("new-segment-id", result.getData().get(0).getId());
        assertEquals("new content", result.getData().get(0).getContent());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createDocumentSegment_ResponseNotSuccessful_ThrowsException() throws IOException {
        // Arrange
        DifyDocumentSegmentCreateRequestDto createRequestDto = DifyDocumentSegmentCreateRequestDto.builder()
            .content("new content")
            .build();
        DifyDocumentSegmentCreateRequestBodyDto createRequest = DifyDocumentSegmentCreateRequestBodyDto.builder()
            .segments(List.of(createRequestDto))
            .build();

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(422);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyApiClient.createDocumentSegment(createRequest));

        assertNotNull(exception);
        assertTrue(exception.getCode().contains(DIFY_API_CALL_FAILED.getCode()));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createDocumentSegment_NullResponseBody_ThrowsException() throws IOException {
        // Arrange
        DifyDocumentSegmentCreateRequestDto createRequestDto = DifyDocumentSegmentCreateRequestDto.builder()
            .content("new content")
            .build();
        DifyDocumentSegmentCreateRequestBodyDto createRequest = DifyDocumentSegmentCreateRequestBodyDto.builder()
            .segments(List.of(createRequestDto))
            .build();

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyApiClient.createDocumentSegment(createRequest));

        assertNotNull(exception);
        assertTrue(exception.getCode().contains(DIFY_RESPONSE_BODY_NULL.getCode()));
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void createDocumentSegment_HttpClientThrowsIOException_PropagatesException() throws IOException {
        // Arrange
        DifyDocumentSegmentCreateRequestDto createRequestDto = DifyDocumentSegmentCreateRequestDto.builder()
            .content("new content")
            .build();
        DifyDocumentSegmentCreateRequestBodyDto createRequest = DifyDocumentSegmentCreateRequestBodyDto.builder()
            .segments(List.of(createRequestDto))
            .build();

        ReflectionTestUtils.setField(difyApiClient, "datasetId", "test-dataset-id");
        ReflectionTestUtils.setField(difyApiClient, "documentId", "test-document-id");
        ReflectionTestUtils.setField(difyApiClient, "knowledgeBaseApiKey", "test-kb-api-key");

        when(httpClient.execute(any(Request.class))).thenThrow(new IOException("Network error"));

        // Act & Assert
        IOException exception = assertThrows(IOException.class,
            () -> difyApiClient.createDocumentSegment(createRequest));

        assertEquals("Network error", exception.getMessage());
        verify(httpClient).execute(any(Request.class));
    }

    @Test
    void callDifyWorkflow_WorkflowFailed() throws IOException {
        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Arrange
            securityUtilMock.when(SecurityUtil::getUserName).thenReturn("test-user");

            String mockStreamData = """
                data: {"event": "workflow_started", "workflow_run_id": "test-workflow-id"}

                data: {"event": "workflow_failed", "data": {"error": "Workflow execution failed"}}

                data: [DONE]
                """;

            when(httpClient.execute(any(Request.class))).thenReturn(response);
            when(response.isSuccessful()).thenReturn(true);
            when(response.body()).thenReturn(responseBody);
            when(responseBody.byteStream()).thenReturn(new ByteArrayInputStream(mockStreamData.getBytes()));

            // Act
            DifyWorkflowResult result = difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE);

            // Assert
            assertNotNull(result);
            assertEquals("test-workflow-id", result.getWorkflowRunId());
            assertEquals("failed", result.getStatus());

            verify(httpClient).execute(any(Request.class));
        }
    }

    @Test
    void createWorkflowPayload_CreatesCorrectStructure() {
        // Test the public behavior instead of private method
        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Arrange
            securityUtilMock.when(SecurityUtil::getUserName).thenReturn("test-user");

            String mockStreamData = """
                data: {"event": "workflow_started", "workflow_run_id": "test-workflow-id"}

                data: {"event": "workflow_succeeded", "data": {"total_tokens": 100, "total_steps": 5, "elapsed_time": 2.5, "outputs": {"result": "success"}}}

                data: [DONE]
                """;

            when(httpClient.execute(any(Request.class))).thenReturn(response);
            when(response.isSuccessful()).thenReturn(true);
            when(response.body()).thenReturn(responseBody);
            when(responseBody.byteStream()).thenReturn(new ByteArrayInputStream(mockStreamData.getBytes()));

            // Act
            DifyWorkflowResult result = difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE);

            // Assert
            assertNotNull(result);
            verify(httpClient).execute(any(Request.class));
            securityUtilMock.verify(SecurityUtil::getUserName);
        } catch (Exception e) {
            fail("Failed to test workflow call: " + e.getMessage());
        }
    }

    @Test
    void callDifyWorkflow_VerifiesRequestStructure() throws IOException {
        // Test that the request is built correctly by verifying the call
        try (MockedStatic<SecurityUtil> securityUtilMock = mockStatic(SecurityUtil.class)) {
            // Arrange
            securityUtilMock.when(SecurityUtil::getUserName).thenReturn("test-user");

            String mockStreamData = """
                data: {"event": "workflow_started", "workflow_run_id": "test-workflow-id"}

                data: {"event": "workflow_succeeded", "data": {"total_tokens": 100, "total_steps": 5, "elapsed_time": 2.5, "outputs": {"result": "success"}}}

                data: [DONE]
                """;

            when(httpClient.execute(any(Request.class))).thenReturn(response);
            when(response.isSuccessful()).thenReturn(true);
            when(response.body()).thenReturn(responseBody);
            when(responseBody.byteStream()).thenReturn(new ByteArrayInputStream(mockStreamData.getBytes()));

            // Act
            DifyWorkflowResult result = difyApiClient.callDifyWorkflow(TEST_KEY, TEST_VALUE);

            // Assert
            assertNotNull(result);
            assertEquals("test-workflow-id", result.getWorkflowRunId());

            // Verify that the HTTP client was called with a request
            verify(httpClient).execute(any(Request.class));
            securityUtilMock.verify(SecurityUtil::getUserName);
        }
    }

    @Test
    void difyApiClient_InitializationTest() {
        // Test that the client is properly initialized
        assertNotNull(difyApiClient);
        assertNotNull(httpClient);

        // Verify that the fields are set correctly
        assertEquals(TEST_API_KEY, ReflectionTestUtils.getField(difyApiClient, "apiKey"));
        assertEquals(TEST_BASE_URL, ReflectionTestUtils.getField(difyApiClient, "baseUrl"));
    }
}
