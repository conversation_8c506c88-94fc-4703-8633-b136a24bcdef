package com.mercaso.ims.infrastructure.util;

import com.mercaso.ims.application.dto.CategoryGroupDto;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.client.ChatClient;

import java.util.List;
import java.util.UUID;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.OPEN_AI_ERROR;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DifyCategoryContentGeneratorTest {

    @Mock
    private ChatClient chatClient;

    @Mock
    private ChatClient.ChatClientRequestSpec requestSpec;

    @Mock
    private ChatClient.ChatClientRequestSpec systemSpec;

    @Mock
    private ChatClient.CallResponseSpec callSpec;

    @InjectMocks
    private DifyCategoryContentGenerator difyCategoryContentGenerator;

    private CategoryGroupDto testCategoryGroup;

    @BeforeEach
    void setUp() {
        testCategoryGroup = CategoryGroupDto.builder()
            .departmentId(UUID.randomUUID())
            .departmentName("Test Department")
            .categoryId(UUID.randomUUID())
            .categoryName("Test Category")
            .subCategoryId(UUID.randomUUID())
            .subCategoryName("Test SubCategory")
            .clazzId(UUID.randomUUID())
            .clazzName("Test Class")
            .build();
    }

    // Tests for generateCategoryContent method
    @Test
    void generateCategoryContent_WithValidCategoryGroup_ReturnsGeneratedContent() {
        // Arrange
        String expectedDescription = "Test description for category";
        String expectedContent = "Product classification task:\nGiven the hierarchical e-commerce category path:\nTest Department (ID: " + 
            testCategoryGroup.getDepartmentId() + ") > Test Category (ID: " + testCategoryGroup.getCategoryId() + 
            ") > Test SubCategory (ID: " + testCategoryGroup.getSubCategoryId() + ") > Test Class (ID: " + 
            testCategoryGroup.getClazzId() + "),\nidentify the most suitable classification for a related product.";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(expectedDescription);

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Product classification task"));
        assertTrue(result.contains("Test Department"));
        assertTrue(result.contains("Test Category"));
        assertTrue(result.contains("Test SubCategory"));
        assertTrue(result.contains("Test Class"));
        
        // Verify AI calls for each category level
        verify(chatClient, times(4)).prompt(); // 4 levels: department, category, sub-category, clazz
        verify(requestSpec, times(4)).system(anyString());
        verify(systemSpec, times(4)).user(anyString());
        verify(callSpec, times(4)).call();
        verify(callSpec, times(4)).content();
    }

    @Test
    void generateCategoryContent_WithNullCategoryGroup_ReturnsEmptyString() {
        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(null);

        // Assert
        assertEquals("", result);
        verify(chatClient, never()).prompt();
    }

    @Test
    void generateCategoryContent_WithPartialCategoryGroup_ReturnsContentWithAvailableLevels() {
        // Arrange
        CategoryGroupDto partialCategoryGroup = CategoryGroupDto.builder()
            .departmentId(UUID.randomUUID())
            .departmentName("Test Department")
            .categoryId(UUID.randomUUID())
            .categoryName("Test Category")
            // Missing sub-category and clazz
            .build();

        String expectedDescription = "Test description for category";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(expectedDescription);

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(partialCategoryGroup);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Test Department"));
        assertTrue(result.contains("Test Category"));
        assertFalse(result.contains("Test SubCategory"));
        assertFalse(result.contains("Test Class"));
        
        // Verify AI calls only for available levels (2 levels)
        verify(chatClient, times(2)).prompt();
    }

    @Test
    void generateCategoryContent_WithEmptyBreakdown_ReturnsEmptyString() {
        // Arrange
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(""); // Empty AI response

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup);

        // Assert
        assertEquals("", result);
        verify(chatClient, atLeastOnce()).prompt();
    }

    @Test
    void generateCategoryContent_WithAIException_ThrowsImsBusinessException() {
        // Arrange
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenThrow(new RuntimeException("AI service error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup));

        assertEquals(OPEN_AI_ERROR.getCode(), exception.getCode());
        verify(chatClient).prompt();
    }

    // Tests for generateKeywords method
    @Test
    void generateKeywords_WithValidContent_ReturnsKeywordList() {
        // Arrange
        String categoryContent = "This is a test category content about electronics and smartphones";
        String aiResponse = "electronics, smartphones, mobile, technology, devices, gadgets, communication, wireless";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(aiResponse);

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertEquals(8, result.size());
        assertTrue(result.contains("electronics"));
        assertTrue(result.contains("smartphones"));
        assertTrue(result.contains("mobile"));
        assertTrue(result.contains("technology"));
        
        verify(chatClient).prompt();
        verify(requestSpec).system(contains("keyword extraction"));
        verify(systemSpec).user(contains(categoryContent));
    }

    @Test
    void generateKeywords_WithEmptyContent_ReturnsEmptyList() {
        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords("");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(chatClient, never()).prompt();
    }

    @Test
    void generateKeywords_WithNullContent_ReturnsEmptyList() {
        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(chatClient, never()).prompt();
    }

    @Test
    void generateKeywords_WithWhitespaceContent_ReturnsEmptyList() {
        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords("   \n\t   ");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(chatClient, never()).prompt();
    }

    @Test
    void generateKeywords_WithDuplicateKeywords_ReturnsDistinctList() {
        // Arrange
        String categoryContent = "Test content";
        String aiResponse = "electronics, smartphones, electronics, mobile, smartphones, technology";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(aiResponse);

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertEquals(4, result.size()); // Duplicates removed
        assertTrue(result.contains("electronics"));
        assertTrue(result.contains("smartphones"));
        assertTrue(result.contains("mobile"));
        assertTrue(result.contains("technology"));
    }

    @Test
    void generateKeywords_WithEmptyAIResponse_ReturnsEmptyList() {
        // Arrange
        String categoryContent = "Test content";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn("");

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(chatClient).prompt();
    }

    @Test
    void generateKeywords_WithAIException_ThrowsImsBusinessException() {
        // Arrange
        String categoryContent = "Test content";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenThrow(new RuntimeException("AI service error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyCategoryContentGenerator.generateKeywords(categoryContent));

        assertEquals(OPEN_AI_ERROR.getCode(), exception.getCode());
        verify(chatClient).prompt();
    }

    @Test
    void generateKeywords_WithMalformedAIResponse_FiltersEmptyKeywords() {
        // Arrange
        String categoryContent = "Test content";
        String aiResponse = "electronics, , smartphones,  , mobile,   , technology, ";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(aiResponse);

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertEquals(4, result.size()); // Empty strings filtered out
        assertTrue(result.contains("electronics"));
        assertTrue(result.contains("smartphones"));
        assertTrue(result.contains("mobile"));
        assertTrue(result.contains("technology"));
        assertFalse(result.contains(""));
        assertFalse(result.contains(" "));
    }

    // Tests for private method behavior through public methods
    @Test
    void buildCategoryPath_WithAllLevels_CreatesCompleteHierarchy() {
        // Arrange
        String expectedDescription = "Test description";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(expectedDescription);

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup);

        // Assert
        assertNotNull(result);
        // Verify the path contains all levels with IDs
        assertTrue(result.contains("Test Department (ID: " + testCategoryGroup.getDepartmentId() + ")"));
        assertTrue(result.contains("Test Category (ID: " + testCategoryGroup.getCategoryId() + ")"));
        assertTrue(result.contains("Test SubCategory (ID: " + testCategoryGroup.getSubCategoryId() + ")"));
        assertTrue(result.contains("Test Class (ID: " + testCategoryGroup.getClazzId() + ")"));
        // Verify hierarchy separator
        assertTrue(result.contains(" > "));
    }

    @Test
    void buildCategoryPath_WithMissingIds_SkipsLevelsWithoutIds() {
        // Arrange
        CategoryGroupDto categoryGroupWithMissingIds = CategoryGroupDto.builder()
            .departmentName("Test Department")
            .departmentId(null) // Missing ID
            .categoryName("Test Category")
            .categoryId(UUID.randomUUID())
            .subCategoryName("Test SubCategory")
            .subCategoryId(null) // Missing ID
            .clazzName("Test Class")
            .clazzId(UUID.randomUUID())
            .build();

        String expectedDescription = "Test description";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(expectedDescription);

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(categoryGroupWithMissingIds);

        // Assert
        assertNotNull(result);
        // Should only contain levels with both name and ID
        assertFalse(result.contains("Test Department"));
        assertTrue(result.contains("Test Category (ID: " + categoryGroupWithMissingIds.getCategoryId() + ")"));
        assertFalse(result.contains("Test SubCategory"));
        assertTrue(result.contains("Test Class (ID: " + categoryGroupWithMissingIds.getClazzId() + ")"));
    }

    @Test
    void buildCategoryPath_WithMissingNames_SkipsLevelsWithoutNames() {
        // Arrange
        CategoryGroupDto categoryGroupWithMissingNames = CategoryGroupDto.builder()
            .departmentName(null) // Missing name
            .departmentId(UUID.randomUUID())
            .categoryName("Test Category")
            .categoryId(UUID.randomUUID())
            .subCategoryName("") // Empty name
            .subCategoryId(UUID.randomUUID())
            .clazzName("Test Class")
            .clazzId(UUID.randomUUID())
            .build();

        String expectedDescription = "Test description";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(expectedDescription);

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(categoryGroupWithMissingNames);

        // Assert
        assertNotNull(result);
        // Should only contain levels with valid names
        assertFalse(result.contains("Test Department"));
        assertTrue(result.contains("Test Category"));
        assertFalse(result.contains("Test SubCategory"));
        assertTrue(result.contains("Test Class"));
    }

    @Test
    void getCategoryDescription_WithValidInput_CallsAIWithCorrectPrompt() {
        // Arrange
        String expectedDescription = "This is a detailed description of the category";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(expectedDescription);

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup);

        // Assert
        assertNotNull(result);

        // Verify the system prompt for category description
        verify(requestSpec, atLeastOnce()).system(contains("e-commerce taxonomy"));

        // Verify user prompts contain category names and levels
        verify(systemSpec, atLeastOnce()).user(contains("Test Department"));
        verify(systemSpec, atLeastOnce()).user(contains("department"));
        verify(systemSpec, atLeastOnce()).user(contains("Test Category"));
        verify(systemSpec, atLeastOnce()).user(contains("category"));
        verify(systemSpec, atLeastOnce()).user(contains("Test SubCategory"));
        verify(systemSpec, atLeastOnce()).user(contains("sub-category"));
        verify(systemSpec, atLeastOnce()).user(contains("Test Class"));
        verify(systemSpec, atLeastOnce()).user(contains("clazz"));
    }

    @Test
    void getCategoryDescription_WithAIError_ReturnsEmptyString() {
        // Arrange
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenThrow(new RuntimeException("AI service temporarily unavailable"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup));

        assertEquals(OPEN_AI_ERROR.getCode(), exception.getCode());
    }

    @Test
    void buildPrompt_IncludesExpectedStructure() {
        // Arrange
        String expectedDescription = "Test description for each level";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(expectedDescription);

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup);

        // Assert
        assertNotNull(result);
        // Verify the prompt structure
        assertTrue(result.contains("Product classification task:"));
        assertTrue(result.contains("Given the hierarchical e-commerce category path:"));
        assertTrue(result.contains("identify the most suitable classification"));
        assertTrue(result.contains("Category breakdown:"));
        assertTrue(result.contains("B2B retail environments"));
        assertTrue(result.contains("granular and relevant"));
    }

    @Test
    void generateKeywords_WithSingleKeyword_ReturnsListWithOneElement() {
        // Arrange
        String categoryContent = "Test content";
        String aiResponse = "electronics";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(aiResponse);

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("electronics", result.get(0));
    }

    @Test
    void generateKeywords_WithKeywordsContainingExtraSpaces_TrimsCorrectly() {
        // Arrange
        String categoryContent = "Test content";
        String aiResponse = "  electronics  ,   smartphones   ,  mobile  ";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(systemSpec);
        when(systemSpec.user(anyString())).thenReturn(callSpec);
        when(callSpec.call()).thenReturn(callSpec);
        when(callSpec.content()).thenReturn(aiResponse);

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("electronics"));
        assertTrue(result.contains("smartphones"));
        assertTrue(result.contains("mobile"));
        // Verify no keywords contain leading/trailing spaces
        result.forEach(keyword -> {
            assertEquals(keyword.trim(), keyword);
        });
    }
}
