package com.mercaso.ims.infrastructure.util;

import com.mercaso.ims.application.dto.CategoryGroupDto;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.client.ChatClient;

import java.util.List;
import java.util.UUID;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.OPEN_AI_ERROR;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DifyCategoryContentGeneratorTest {

    @Mock
    private ChatClient chatClient;

    @InjectMocks
    private DifyCategoryContentGenerator difyCategoryContentGenerator;

    private CategoryGroupDto testCategoryGroup;

    @BeforeEach
    void setUp() {
        testCategoryGroup = CategoryGroupDto.builder()
            .departmentId(UUID.randomUUID())
            .departmentName("Test Department")
            .categoryId(UUID.randomUUID())
            .categoryName("Test Category")
            .subCategoryId(UUID.randomUUID())
            .subCategoryName("Test SubCategory")
            .clazzId(UUID.randomUUID())
            .clazzName("Test Class")
            .build();
    }

    @Test
    void generateCategoryContent_WithValidCategoryGroup_ReturnsGeneratedContent() {
        // Arrange
        String expectedDescription = "Test description for category";
        
        // Mock the entire fluent API chain
        ChatClient.PromptUserSpec promptUserSpec = mock(ChatClient.PromptUserSpec.class);
        ChatClient.CallPromptSpec callPromptSpec = mock(ChatClient.CallPromptSpec.class);
        ChatClient.ChatResponse chatResponse = mock(ChatClient.ChatResponse.class);
        
        when(chatClient.prompt()).thenReturn(promptUserSpec);
        when(promptUserSpec.system(anyString())).thenReturn(promptUserSpec);
        when(promptUserSpec.user(anyString())).thenReturn(callPromptSpec);
        when(callPromptSpec.call()).thenReturn(chatResponse);
        when(chatResponse.content()).thenReturn(expectedDescription);

        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("Product classification task"));
        assertTrue(result.contains("Test Department"));
        assertTrue(result.contains("Test Category"));
        assertTrue(result.contains("Test SubCategory"));
        assertTrue(result.contains("Test Class"));
        
        // Verify AI calls for each category level
        verify(chatClient, times(4)).prompt(); // 4 levels: department, category, sub-category, clazz
    }

    @Test
    void generateCategoryContent_WithNullCategoryGroup_ReturnsEmptyString() {
        // Act
        String result = difyCategoryContentGenerator.generateCategoryContent(null);

        // Assert
        assertEquals("", result);
        verify(chatClient, never()).prompt();
    }

    @Test
    void generateCategoryContent_WithAIException_ThrowsImsBusinessException() {
        // Arrange
        ChatClient.PromptUserSpec promptUserSpec = mock(ChatClient.PromptUserSpec.class);
        
        when(chatClient.prompt()).thenReturn(promptUserSpec);
        when(promptUserSpec.system(anyString())).thenReturn(promptUserSpec);
        when(promptUserSpec.user(anyString())).thenThrow(new RuntimeException("AI service error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyCategoryContentGenerator.generateCategoryContent(testCategoryGroup));

        assertEquals(OPEN_AI_ERROR.getCode(), exception.getCode());
        verify(chatClient).prompt();
    }

    @Test
    void generateKeywords_WithValidContent_ReturnsKeywordList() {
        // Arrange
        String categoryContent = "This is a test category content about electronics and smartphones";
        String aiResponse = "electronics, smartphones, mobile, technology, devices, gadgets, communication, wireless";

        ChatClient.PromptUserSpec promptUserSpec = mock(ChatClient.PromptUserSpec.class);
        ChatClient.CallPromptSpec callPromptSpec = mock(ChatClient.CallPromptSpec.class);
        ChatClient.ChatResponse chatResponse = mock(ChatClient.ChatResponse.class);
        
        when(chatClient.prompt()).thenReturn(promptUserSpec);
        when(promptUserSpec.system(anyString())).thenReturn(promptUserSpec);
        when(promptUserSpec.user(anyString())).thenReturn(callPromptSpec);
        when(callPromptSpec.call()).thenReturn(chatResponse);
        when(chatResponse.content()).thenReturn(aiResponse);

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertEquals(8, result.size());
        assertTrue(result.contains("electronics"));
        assertTrue(result.contains("smartphones"));
        assertTrue(result.contains("mobile"));
        assertTrue(result.contains("technology"));

        verify(chatClient).prompt();
    }

    @Test
    void generateKeywords_WithEmptyContent_ReturnsEmptyList() {
        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords("");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(chatClient, never()).prompt();
    }

    @Test
    void generateKeywords_WithNullContent_ReturnsEmptyList() {
        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(chatClient, never()).prompt();
    }

    @Test
    void generateKeywords_WithAIException_ThrowsImsBusinessException() {
        // Arrange
        String categoryContent = "Test content";
        
        ChatClient.PromptUserSpec promptUserSpec = mock(ChatClient.PromptUserSpec.class);
        
        when(chatClient.prompt()).thenReturn(promptUserSpec);
        when(promptUserSpec.system(anyString())).thenReturn(promptUserSpec);
        when(promptUserSpec.user(anyString())).thenThrow(new RuntimeException("AI service error"));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> difyCategoryContentGenerator.generateKeywords(categoryContent));

        assertEquals(OPEN_AI_ERROR.getCode(), exception.getCode());
        verify(chatClient).prompt();
    }

    @Test
    void generateKeywords_WithEmptyAIResponse_ReturnsEmptyList() {
        // Arrange
        String categoryContent = "Test content";

        ChatClient.PromptUserSpec promptUserSpec = mock(ChatClient.PromptUserSpec.class);
        ChatClient.CallPromptSpec callPromptSpec = mock(ChatClient.CallPromptSpec.class);
        ChatClient.ChatResponse chatResponse = mock(ChatClient.ChatResponse.class);
        
        when(chatClient.prompt()).thenReturn(promptUserSpec);
        when(promptUserSpec.system(anyString())).thenReturn(promptUserSpec);
        when(promptUserSpec.user(anyString())).thenReturn(callPromptSpec);
        when(callPromptSpec.call()).thenReturn(chatResponse);
        when(chatResponse.content()).thenReturn("");

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(chatClient).prompt();
    }

    @Test
    void generateKeywords_WithDuplicateKeywords_ReturnsDistinctList() {
        // Arrange
        String categoryContent = "Test content";
        String aiResponse = "electronics, smartphones, electronics, mobile, smartphones, technology";

        ChatClient.PromptUserSpec promptUserSpec = mock(ChatClient.PromptUserSpec.class);
        ChatClient.CallPromptSpec callPromptSpec = mock(ChatClient.CallPromptSpec.class);
        ChatClient.ChatResponse chatResponse = mock(ChatClient.ChatResponse.class);
        
        when(chatClient.prompt()).thenReturn(promptUserSpec);
        when(promptUserSpec.system(anyString())).thenReturn(promptUserSpec);
        when(promptUserSpec.user(anyString())).thenReturn(callPromptSpec);
        when(callPromptSpec.call()).thenReturn(chatResponse);
        when(chatResponse.content()).thenReturn(aiResponse);

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertEquals(4, result.size()); // Should be distinct
        assertTrue(result.contains("electronics"));
        assertTrue(result.contains("smartphones"));
        assertTrue(result.contains("mobile"));
        assertTrue(result.contains("technology"));
    }

    @Test
    void generateKeywords_WithMalformedAIResponse_FiltersEmptyKeywords() {
        // Arrange
        String categoryContent = "Test content";
        String aiResponse = "electronics, , smartphones,  , mobile,   , technology, ";

        ChatClient.PromptUserSpec promptUserSpec = mock(ChatClient.PromptUserSpec.class);
        ChatClient.CallPromptSpec callPromptSpec = mock(ChatClient.CallPromptSpec.class);
        ChatClient.ChatResponse chatResponse = mock(ChatClient.ChatResponse.class);
        
        when(chatClient.prompt()).thenReturn(promptUserSpec);
        when(promptUserSpec.system(anyString())).thenReturn(promptUserSpec);
        when(promptUserSpec.user(anyString())).thenReturn(callPromptSpec);
        when(callPromptSpec.call()).thenReturn(chatResponse);
        when(chatResponse.content()).thenReturn(aiResponse);

        // Act
        List<String> result = difyCategoryContentGenerator.generateKeywords(categoryContent);

        // Assert
        assertNotNull(result);
        assertEquals(4, result.size()); // Should filter out empty strings
        assertTrue(result.contains("electronics"));
        assertTrue(result.contains("smartphones"));
        assertTrue(result.contains("mobile"));
        assertTrue(result.contains("technology"));
        // Verify no keywords contain leading/trailing spaces
        result.forEach(keyword -> {
            assertEquals(keyword.trim(), keyword);
        });
    }
}
