package com.mercaso.wms.application.mapper.crossdock;

import com.mercaso.wms.application.dto.CrossDockTaskDto;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
    uses = CrossDockTaskItemDtoApplicationMapper.class,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CrossDockTaskDtoApplicationMapper extends BaseDtoApplicationMapper<CrossDockTask, CrossDockTaskDto> {

    CrossDockTaskDtoApplicationMapper INSTANCE = Mappers.getMapper(CrossDockTaskDtoApplicationMapper.class);

}
