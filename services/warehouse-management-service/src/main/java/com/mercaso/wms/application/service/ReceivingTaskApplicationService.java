package com.mercaso.wms.application.service;

import static com.mercaso.wms.batch.enums.SourceEnum.onlineVendors;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.receivingtask.UpdateReceivingTaskCommand;
import com.mercaso.wms.application.dto.event.ReceivingTaskCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.ReceivingTaskReceivedPayloadDto;
import com.mercaso.wms.application.dto.event.ReceivingTaskStartedPayloadDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskItemDto;
import com.mercaso.wms.application.mapper.receivingtask.ReceivingTaskDtoApplicationMapper;
import com.mercaso.wms.application.mapper.receivingtask.ReceivingTaskItemDtoApplicationMapper;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItemRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReceivingTaskApplicationService {

    private final ReceivingTaskRepository receivingTaskRepository;

    private final BatchItemQueryService batchItemQueryService;

    private final BusinessEventDispatcher businessEventDispatcher;

    private final ReceivingTaskDtoApplicationMapper receivingTaskDtoApplicationMapper;

    private final ReceivingTaskItemRepository receivingTaskItemRepository;

    private final ReceivingTaskItemDtoApplicationMapper receivingTaskItemDtoApplicationMapper;

    private final PgAdvisoryLock pgAdvisoryLock;

    private final ApplicationEventDispatcher applicationEventDispatcher;

    private final CrossDockTaskItemService crossDockTaskItemService;

    @RetryableTransaction
    public void createReceivingTask(UUID batchId) {
        onlineVendors().forEach(source -> create(batchId, source));
    }

    private void create(UUID batchId, SourceEnum source) {
        List<BatchItem> batchItems = batchItemQueryService.findBy(batchId, source.name());
        if (batchItems.isEmpty()) {
            log.info("No batch items found for {}: {}", source, batchId);
            return;
        }
        ReceivingTaskDto receivingTaskDto = receivingTaskDtoApplicationMapper.domainToDto(receivingTaskRepository.save(
            ReceivingTask.builder().build().create(batchId, batchItems, source)));
        businessEventDispatcher.dispatch(BusinessEventFactory.build(ReceivingTaskCreatedPayloadDto.builder()
            .receivingTaskId(receivingTaskDto.getId())
            .data(receivingTaskDto)
            .build()));
        log.info("Receiving task created for {}", source);
    }

    @RetryableTransaction
    public ReceivingTaskItemDto scanReceiveItem(UUID receivingTaskItemId) {
        pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock(receivingTaskItemId.hashCode(), "ScanReceiveItem");
        ReceivingTaskItem receivingTaskItem = receivingTaskItemRepository.findById(receivingTaskItemId);
        if (receivingTaskItem == null) {
            throw new WmsBusinessException(ErrorCodeEnums.RECEIVING_TASK_ITEM_NOT_FOUND.getCode(),
                ErrorCodeEnums.RECEIVING_TASK_ITEM_NOT_FOUND.getMessage());
        }
        if (receivingTaskItem.getReceivedQty() >= receivingTaskItem.getExpectQty()) {
            log.info("[scanReceiveItem] Receiving task item has received: {} by {}",
                receivingTaskItemId,
                SecurityContextUtil.getUsername());
            throw new WmsBusinessException(ErrorCodeEnums.RECEIVING_TASK_ITEM_HAS_RECEIVED.getCode(),
                ErrorCodeEnums.RECEIVING_TASK_ITEM_HAS_RECEIVED.getMessage());
        }
        int oldReceivedQty = receivingTaskItem.getReceivedQty();

        ReceivingTask receivingTask = startReceiving(receivingTaskItem.getReceivingTaskId());
        if (ReceivingTaskStatus.RECEIVED == receivingTask.getStatus()) {
            throw new WmsBusinessException(ErrorCodeEnums.RECEIVING_TASK_HAS_RECEIVED.getCode(),
                ErrorCodeEnums.RECEIVING_TASK_HAS_RECEIVED.getMessage());
        }
        ReceivingTaskItem updatedReceivingTaskItem = receivingTask.scanReceive(receivingTaskItemId);
        int newReceivedQty = updatedReceivingTaskItem.getReceivedQty() == null ? 0 : updatedReceivingTaskItem.getReceivedQty();

        log.info("[scanReceiveItem] Receiving task item scanned: {} by {}",
            receivingTaskItemId,
            SecurityContextUtil.getUsername());
        ReceivingTask updatedReceivingTask = receivingTaskRepository.save(receivingTask);
        if (ReceivingTaskStatus.RECEIVED == updatedReceivingTask.getStatus()) {
            DispatchResponseDto responseDto = businessEventDispatcher.dispatch(BusinessEventFactory.build(
                ReceivingTaskReceivedPayloadDto.builder()
                    .receivingTaskId(updatedReceivingTask.getId())
                    .data(receivingTaskDtoApplicationMapper.domainToDto(updatedReceivingTask))
                    .build()));
            applicationEventDispatcher.publishEvent(responseDto.getEvent());
        }

        try {
            if (oldReceivedQty < newReceivedQty) {
                Map<UUID, Integer> qtyChangeMap = Map.of(
                        updatedReceivingTaskItem.getId(),
                        newReceivedQty
                );
                crossDockTaskItemService.handleTaskItemQtyChange(qtyChangeMap, CrossDockItemSourceEnum.RECEIVING_TASK);
            }
        } catch (Exception e) {
            log.error("[scanReceiveItem] Failed to handle cross dock item for receivingTaskItemId={}, oldQty={}, newQty={}, error={}",
                    updatedReceivingTaskItem.getId(), oldReceivedQty, newReceivedQty, e.getMessage(), e);
        }

        return receivingTaskItemDtoApplicationMapper.domainToDto(updatedReceivingTaskItem);
    }

    @RetryableTransaction
    public ReceivingTask startReceiving(UUID receivingTaskId) {
        ReceivingTask receivingTask = receivingTaskRepository.findById(receivingTaskId);
        if (receivingTask == null || receivingTask.getStatus() == ReceivingTaskStatus.CANCELED) {
            throw new WmsBusinessException(ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getCode(),
                ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getMessage());
        }
        if (ReceivingTaskStatus.CREATED == receivingTask.getStatus()) {
            receivingTask.start();
            ReceivingTask updated = receivingTaskRepository.save(receivingTask);
            businessEventDispatcher.dispatch(BusinessEventFactory.build(ReceivingTaskStartedPayloadDto.builder()
                .receivingTaskId(updated.getId())
                .data(receivingTaskDtoApplicationMapper.domainToDto(updated))
                .build()));
            return updated;
        }
        return receivingTask;
    }

    @RetryableTransaction
    public ReceivingTaskDto receive(UUID receivingTaskId) {
        ReceivingTask receivingTask = receivingTaskRepository.findById(receivingTaskId);
        if (receivingTask == null) {
            throw new WmsBusinessException(ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getCode(),
                ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getMessage());
        }
        if (receivingTask.getStatus() == ReceivingTaskStatus.RECEIVED) {
            throw new WmsBusinessException(ErrorCodeEnums.RECEIVING_TASK_HAS_RECEIVED.getCode(),
                ErrorCodeEnums.RECEIVING_TASK_HAS_RECEIVED.getMessage());
        }
        receivingTask.received();
        log.info("[receive] Receiving task received: {} by {}", receivingTaskId, SecurityContextUtil.getUsername());
        ReceivingTaskDto receivingTaskDto = receivingTaskDtoApplicationMapper.domainToDto(receivingTaskRepository.save(
            receivingTask));
        DispatchResponseDto responseDto = businessEventDispatcher.dispatch(BusinessEventFactory.build(
            ReceivingTaskReceivedPayloadDto.builder()
                .receivingTaskId(receivingTaskDto.getId())
                .data(receivingTaskDto)
                .build()));
        applicationEventDispatcher.publishEvent(responseDto.getEvent());
        return receivingTaskDto;
    }

    @RetryableTransaction
    public ReceivingTaskDto updateReceivingTask(UpdateReceivingTaskCommand command) {
        ReceivingTask receivingTask = receivingTaskRepository.findById(command.getReceivingTaskId());
        if (receivingTask == null) {
            throw new WmsBusinessException(ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getCode(),
                ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getMessage());
        }
        receivingTask.update(command);
        log.info("[updateReceivingTaskItem] Receiving task item updated: {} by {}",
            receivingTask.getId(),
            SecurityContextUtil.getUsername());
        return receivingTaskDtoApplicationMapper.domainToDto(receivingTaskRepository.save(receivingTask));
    }

}
