package com.mercaso.wms.batch.util;

import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.LongSummaryStatistics;
import java.util.Map;
import java.util.UUID;

public class ClosestSumFinder {

    private ClosestSumFinder() {
    }

    @SuppressWarnings("unchecked")
    public static List<ExcelBatchDto> findClosestSumWithMaxElements(List<ExcelBatchDto> excelBatchDtos, int totalStock) {
        List<ExcelBatchDto>[] dp = new ArrayList[totalStock + 1];
        dp[0] = new ArrayList<>();

        for (ExcelBatchDto excelBatchDto : excelBatchDtos) {
            int qty = excelBatchDto.getQuantity();

            for (int i = totalStock; i >= qty; i--) {
                if (dp[i - qty] != null) {
                    List<ExcelBatchDto> newCombination = new ArrayList<>(dp[i - qty]);
                    newCombination.add(excelBatchDto);

                    if (dp[i] == null || newCombination.size() > dp[i].size() ||
                        (newCombination.size() == dp[i].size() && sum(newCombination) > sum(dp[i]))) {
                        dp[i] = newCombination;
                    }
                }
            }
        }

        for (int i = totalStock; i >= 0; i--) {
            if (dp[i] != null) {
                dp[i].sort(Comparator.comparingInt(ExcelBatchDto::getQuantity).reversed());
                return dp[i];
            }
        }
        return new ArrayList<>();
    }

    private static int sum(List<ExcelBatchDto> excelBatchDtos) {
        return excelBatchDtos.stream().mapToInt(ExcelBatchDto::getQuantity).sum();
    }

    /**
     * Find the closest combination of orders where the sum is closest to the target, allowing only for tolerance in cases where
     * the sum exceeds the target.
     */
    public static List<String> findClosestOrders(Map<String, LongSummaryStatistics> orderQtyMap, long target, long tolerance) {
        List<Map.Entry<String, LongSummaryStatistics>> sortedOrders = new ArrayList<>(orderQtyMap.entrySet());

        long currentSum = 0;
        List<String> selectedOrders = new ArrayList<>();

        for (Map.Entry<String, LongSummaryStatistics> entry : sortedOrders) {
            long orderQty = entry.getValue().getSum();

            // Try adding this order to the combination
            if (currentSum + orderQty <= target + tolerance) {
                selectedOrders.add(entry.getKey());
                currentSum += orderQty;
            }

            // If the sum is within the target range, stop early
            if (currentSum >= target) {
                break;
            }
        }

        return selectedOrders;
    }

    public static List<List<String>> findClosestTask(Map<String, LongSummaryStatistics> orderQtyMap,
                                                    long target,
                                                    long tolerance,
                                                    int splitCount) {
        List<List<String>> groups = initializeGroups(splitCount);
        long[] groupSums = new long[splitCount];
        
        List<Map.Entry<String, LongSummaryStatistics>> sortedOrders = getSortedOrders(orderQtyMap);

        for (Map.Entry<String, LongSummaryStatistics> entry : sortedOrders) {
            String orderId = entry.getKey();
            long orderQty = entry.getValue().getSum();

            int bestGroupIndex = findBestGroup(groupSums, orderQty, target, tolerance);
            
            if (bestGroupIndex != -1) {
                groups.get(bestGroupIndex).add(orderId);
                groupSums[bestGroupIndex] += orderQty;
            } else {
                assignToEmptyGroup(groups, groupSums, orderId, orderQty);
            }
        }

        return groups;
    }

    private static List<List<String>> initializeGroups(int splitCount) {
        List<List<String>> groups = new ArrayList<>();
        for (int i = 0; i < splitCount; i++) {
            groups.add(new ArrayList<>());
        }
        return groups;
    }

    private static List<Map.Entry<String, LongSummaryStatistics>> getSortedOrders(
            Map<String, LongSummaryStatistics> orderQtyMap) {
        List<Map.Entry<String, LongSummaryStatistics>> sortedOrders = 
            new ArrayList<>(orderQtyMap.entrySet());
        sortedOrders.sort((o1, o2) -> Long.compare(o2.getValue().getSum(), o1.getValue().getSum()));
        return sortedOrders;
    }

    private static int findBestGroup(long[] groupSums, long orderQty, long target, long tolerance) {
        int bestGroup = -1;
        long bestGroupDiff = Long.MAX_VALUE;

        for (int i = 0; i < groupSums.length; i++) {
            long potentialSum = groupSums[i] + orderQty;
            if (potentialSum <= target + tolerance) {
                long diff = Math.abs(target - potentialSum);
                if (diff < bestGroupDiff) {
                    bestGroup = i;
                    bestGroupDiff = diff;
                }
            }
        }
        return bestGroup;
    }

    private static void assignToEmptyGroup(List<List<String>> groups, long[] groupSums, 
                                         String orderId, long orderQty) {
        if (groupSums.length == 0) {
            return;
        }
        int minGroup = findMinSumGroup(groupSums);
        if (groupSums[minGroup] == 0) {
            groups.get(minGroup).add(orderId);
            groupSums[minGroup] += orderQty;
        }
    }

    private static int findMinSumGroup(long[] groupSums) {
        int minGroup = 0;
        for (int i = 1; i < groupSums.length; i++) {
            if (groupSums[i] < groupSums[minGroup]) {
                minGroup = i;
            }
        }
        return minGroup;
    }

    public static List<List<UUID>> splitMapWith(Map<UUID, LongSummaryStatistics> map) {
        Map.Entry<UUID, LongSummaryStatistics> maxEntry = map.entrySet().stream()
            .max(Comparator.comparingLong(e -> e.getValue().getSum()))
            .orElseThrow(() -> new IllegalArgumentException("Map cannot be empty"));

        Map<UUID, LongSummaryStatistics> remainingMap = new HashMap<>(map);
        remainingMap.remove(maxEntry.getKey());

        List<Map.Entry<UUID, LongSummaryStatistics>> sortedEntries = remainingMap.entrySet()
            .stream()
            .sorted((e1, e2) -> Long.compare(e2.getValue().getSum(), e1.getValue().getSum()))
            .toList();

        List<UUID> group1 = new ArrayList<>();
        List<UUID> group2 = new ArrayList<>();
        group1.add(maxEntry.getKey());
        long sum1 = maxEntry.getValue().getSum();
        long sum2 = 0;

        for (Map.Entry<UUID, LongSummaryStatistics> entry : sortedEntries) {
            UUID key = entry.getKey();
            long value = entry.getValue().getSum();

            if (sum1 <= sum2) {
                group1.add(key);
                sum1 += value;
            } else {
                group2.add(key);
                sum2 += value;
            }
        }

        return Arrays.asList(group1, group2);
    }

    public static List<List<PickingTask>> splitTasks(List<PickingTask> pickingTasks, int splitCount) {
        List<List<PickingTask>> result = new ArrayList<>();
        if (pickingTasks.isEmpty() || splitCount <= 0) {
            return result;
        }

        // Calculate total quantity across all tasks
        long totalQty = pickingTasks.stream()
            .mapToLong(task -> task.getPickingTaskItems().stream()
                .mapToLong(PickingTaskItem::getExpectQty)
                .sum())
            .sum();

        // Calculate target quantity per group
        long targetQtyPerGroup = totalQty / splitCount;

        List<PickingTask> currentGroup = new ArrayList<>();
        long currentGroupQty = 0;

        for (PickingTask task : pickingTasks) {
            long taskQty = task.getPickingTaskItems().stream()
                .mapToLong(PickingTaskItem::getExpectQty)
                .sum();

            currentGroup.add(task);
            currentGroupQty += taskQty;

            // Check if current group has reached target quantity
            if (currentGroupQty >= targetQtyPerGroup && result.size() < splitCount - 1) {
                result.add(new ArrayList<>(currentGroup));
                currentGroup.clear();
                currentGroupQty = 0;
            }
        }

        // Add remaining tasks to the last group
        if (!currentGroup.isEmpty()) {
            result.add(currentGroup);
        }

        return result;
    }

}