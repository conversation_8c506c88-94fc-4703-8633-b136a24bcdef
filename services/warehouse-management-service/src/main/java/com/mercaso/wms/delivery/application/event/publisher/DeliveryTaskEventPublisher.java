package com.mercaso.wms.delivery.application.event.publisher;

import com.mercaso.businessevents.client.BusinessEventClient;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskBuildPayloadDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryTaskBuildEventDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryTaskBuildEventDto.OrderInfo;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryTaskEventPublisher {

    private final BusinessEventClient businessEventClient;

    public void publishDeliveryTaskBuildEvent(DeliveryTask task, List<DeliveryOrder> allOrdersToUpdate) {
        if (task == null) {
            log.warn("Delivery task is null, cannot publish event.");
            return;
        }

        if (CollectionUtils.isEmpty(allOrdersToUpdate)) {
            log.info("No orders to update for task ID: {}, skipping event publication.", task.getId());
            return;
        }

        UUID taskId = task.getId();

        List<DeliveryOrder> ordersBelongToTask = allOrdersToUpdate.stream()
            .filter(order -> taskId.equals(order.getDeliveryTaskId()))
            .toList();

        if (CollectionUtils.isEmpty(ordersBelongToTask)) {
            log.info("No orders belong to the delivery task with ID: {}, skipping event publication.", taskId);
            return;
        }

        DeliveryTaskBuildPayloadDto payload = buildDeliveryTaskBuildPayloadDto(task, ordersBelongToTask);

        log.info("Publishing delivery task build event: taskId={}, payload={}", taskId, payload);
        businessEventClient.dispatch(BusinessEventFactory.build(payload));
    }

    private static DeliveryTaskBuildPayloadDto buildDeliveryTaskBuildPayloadDto(DeliveryTask task,
        List<DeliveryOrder> ordersBelongToTask) {

        return DeliveryTaskBuildPayloadDto.builder()
            .deliveryTaskId(task.getId())
            .data(DeliveryTaskBuildEventDto.builder()
                .deliveryTaskNumber(task.getNumber())
                .deliveryDate(task.getDeliveryDate())
                .orders(ordersBelongToTask.stream()
                    .map(order -> OrderInfo.builder()
                        .orderId(order.getId())
                        .orderNumber(order.getOrderNumber())
                        .deliveryDate(order.getDeliveryDate())
                        .status(order.getStatus())
                        .build())
                    .toList())
                .build())
            .build();
    }
}
