package com.mercaso.wms.delivery.application.search;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryOrderView;
import com.mercaso.wms.delivery.application.dto.view.SearchPaymentSummaryView;
import com.mercaso.wms.delivery.application.query.DeliveryOrderQuery;
import com.mercaso.wms.delivery.application.query.PaymentSummaryQuery;
import com.mercaso.wms.delivery.config.PaymentSummaryExportConfig;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.DeliveryOrderJdbcTemplate;
import jakarta.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class DeliveryOrderSearchService {

    private final DeliveryOrderJdbcTemplate jdbcTemplate;
    private final PaymentSummaryExportConfig exportConfig;

    private static final String PAYMENT_TYPE_SEPARATOR = ",";
    private static final String PAYMENT_TYPE_JOIN_SEPARATOR = ", ";

    public Page<SearchDeliveryOrderView> search(DeliveryOrderQuery deliveryOrderQuery, Pageable pageable) {
        return jdbcTemplate.search(deliveryOrderQuery, pageable);
    }

    public Page<SearchPaymentSummaryView> searchPaymentSummary(@Valid PaymentSummaryQuery query, PageRequest pageRequest) {
        log.info("Searching payment summary with query: {}", query);
        return jdbcTemplate.searchPaymentSummary(query, pageRequest);
    }

    public ByteArrayOutputStream exportPaymentSummary(String deliveryDate) {
        log.info("Exporting payment summary with delivery date: {}", deliveryDate);

        PageRequest pageRequest = PageRequest.of(0, exportConfig.getMaxPageSize());
        PaymentSummaryQuery query = PaymentSummaryQuery.builder()
            .deliveryDate(deliveryDate)
            .build();

        Page<SearchPaymentSummaryView> paymentSummary = searchPaymentSummary(query, pageRequest);
        List<SearchPaymentSummaryView> paymentSummaryViews = paymentSummary.getContent();

        if (CollectionUtils.isEmpty(paymentSummaryViews)) {
            log.warn("No payment summary found for delivery date: {}", deliveryDate);
            throw new DeliveryBadRequestException("No payment summary found for delivery date: " + deliveryDate);
        }

        processPaymentTypes(paymentSummaryViews);

        return generateExcel(paymentSummaryViews);

    }

    private void processPaymentTypes(List<SearchPaymentSummaryView> paymentSummaryViews) {

        paymentSummaryViews.forEach(view -> {
            String paymentType = view.getPaymentType();

            if (StringUtils.isBlank(paymentType)) {
                return;
            }

            String formattedPaymentTypes = Arrays.stream(paymentType.split(PAYMENT_TYPE_SEPARATOR))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(PaymentType::getValue)
                .collect(Collectors.joining(PAYMENT_TYPE_JOIN_SEPARATOR));

            view.setPaymentType(StringUtils.defaultIfBlank(formattedPaymentTypes, ""));
        });
    }

    private ByteArrayOutputStream generateExcel(List<SearchPaymentSummaryView> paymentSummaryViews) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        return writeBatchTemplate(paymentSummaryViews, outputStream);
    }

    public ByteArrayOutputStream writeBatchTemplate(List<SearchPaymentSummaryView> paymentSummaryViews,
        ByteArrayOutputStream outputStream) {

        Resource resource = new ClassPathResource(exportConfig.getTemplatePath());

        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream)
            .withTemplate(resource.getInputStream())
            .build()) {

            FillConfig fillConfig = FillConfig.builder()
                .forceNewRow(Boolean.FALSE)
                .build();

            excelWriter.fill(paymentSummaryViews,
                fillConfig,
                EasyExcelFactory.writerSheet(GeneratedDocNameEnum.DA_DAILY_LOG.getValue()).build());

        } catch (IOException e) {
            throw new DeliveryBusinessException("Failed to generate the payment summary Excel file", e);
        }

        return outputStream;
    }
}
