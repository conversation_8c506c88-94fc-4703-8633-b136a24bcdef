package com.mercaso.wms.delivery.infrastructure.config;

import com.mercaso.wms.delivery.infrastructure.interceptor.SingleDeviceLoginCheckInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Configuration class for registering custom interceptors.
 */
@Configuration
@RequiredArgsConstructor
public class InterceptorConfig implements WebMvcConfigurer {

    private final SingleDeviceLoginCheckInterceptor singleDeviceLoginCheckInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(singleDeviceLoginCheckInterceptor);
    }
} 