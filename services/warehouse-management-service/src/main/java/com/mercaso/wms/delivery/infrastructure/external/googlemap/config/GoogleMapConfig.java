package com.mercaso.wms.delivery.infrastructure.external.googlemap.config;

import com.google.maps.GeoApiContext;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class GoogleMapConfig {
    
    private final GoogleMapProperties properties;
    
    @Bean
    public GeoApiContext geoApiContext() {
        return new GeoApiContext.Builder()
                .apiKey(properties.getApiKey())
                .connectTimeout(properties.getConnectTimeout(), java.util.concurrent.TimeUnit.MILLISECONDS)
                .readTimeout(properties.getReadTimeout(), java.util.concurrent.TimeUnit.MILLISECONDS)
                .build();
    }
} 