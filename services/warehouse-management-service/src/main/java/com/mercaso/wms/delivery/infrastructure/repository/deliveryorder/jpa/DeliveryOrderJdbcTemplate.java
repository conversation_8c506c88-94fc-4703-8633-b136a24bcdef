package com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa;

import com.mercaso.wms.delivery.application.dto.deliveryorder.AddressDto;
import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryOrderView;
import com.mercaso.wms.delivery.application.dto.view.SearchPaymentSummaryView;
import com.mercaso.wms.delivery.application.query.DeliveryOrderQuery;
import com.mercaso.wms.delivery.application.query.PaymentSummaryQuery;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeliveryOrderJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<SearchDeliveryOrderView> search(DeliveryOrderQuery query, Pageable pageable) {
        MapSqlParameterSource params = new MapSqlParameterSource();

        DeliveryOrderSearchSqlBuilder sqlBuilder = DeliveryOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        String selectSql = sqlBuilder.buildSelectSql();
        String countSql = sqlBuilder.buildCountSql();

        Long total = jdbcTemplate.queryForObject(countSql, params, Long.class);

        List<SearchDeliveryOrderView> content = jdbcTemplate.query(selectSql, params,
            (rs, rowNum) -> convert(rs));

        return new PageImpl<>(content, pageable, total != null ? total : 0);
    }

    private SearchDeliveryOrderView convert(ResultSet rs) throws SQLException {
        SearchDeliveryOrderView view = new SearchDeliveryOrderView();
        view.setId(rs.getObject("id", UUID.class));
        view.setOrderNumber(rs.getString("order_number"));
        view.setStatus(rs.getString("status"));
        view.setDeliveryDate(rs.getString("delivery_date"));
        view.setDeliveryTimeWindow(rs.getString("delivery_time_window"));
        view.setPaymentType(rs.getString("payment_type"));
        view.setPaymentStatus(rs.getString("payment_status"));
        view.setFulfillmentStatus(rs.getString("fulfillment_status"));
        view.setCustomerNotes(rs.getString("customer_notes"));
        view.setNotes(rs.getString("notes"));
        view.setSequence(rs.getInt("sequence"));
        view.setCurrentQty(rs.getBigDecimal("currentQty"));
        view.setDeliveredQty(rs.getBigDecimal("deliveredQty"));
        view.setRescheduleType(rs.getString("reschedule_type"));
        view.setPlanArriveAt(rs.getTimestamp("plan_arrive_at") != null ? rs.getTimestamp("plan_arrive_at").toInstant() : null);
        view.setPlanDeliveryAt(
            rs.getTimestamp("plan_delivery_at") != null ? rs.getTimestamp("plan_delivery_at").toInstant() : null);
        view.setArrivedAt(rs.getTimestamp("arrived_at") != null ? rs.getTimestamp("arrived_at").toInstant() : null);
        view.setUnloadedAt(
            rs.getTimestamp("unloaded_at") != null ? rs.getTimestamp("unloaded_at").toInstant() : null);
        view.setDeliveredAt(rs.getTimestamp("delivered_at") != null ? rs.getTimestamp("delivered_at").toInstant() : null);
        // Delivery task information
        view.setDeliveryTaskId(rs.getObject("delivery_task_id", UUID.class));
        view.setDeliveryTaskNumber(rs.getString("delivery_task_number"));
        view.setDriverUserName(rs.getString("driver_user_name"));
        view.setTruckNumber(rs.getString("truck_number"));
        String address = rs.getString("address");
        if (address != null) {
            try {
                view.setAddress(SerializationUtils.deserialize(address, AddressDto.class));
            } catch (IOException e) {
                log.warn("[DeliveryOrderJdbcTemplate] Failed to deserialize address", e);
            }
        }
        view.setOriginalTotalPrice(rs.getBigDecimal("original_total_price"));
        view.setTotalPrice(rs.getBigDecimal("total_price"));
        return view;
    }

    public Page<SearchPaymentSummaryView> searchPaymentSummary(PaymentSummaryQuery query, PageRequest pageRequest) {
        log.info("Searching payment summary with query: {}, pageRequest: {}", query, pageRequest);

        MapSqlParameterSource params = new MapSqlParameterSource();

        PaymentSummarySearchSqlBuilder sqlBuilder = PaymentSummarySearchSqlBuilder.builder()
            .query(query)
            .pageable(pageRequest)
            .params(params)
            .build();

        String selectSql = sqlBuilder.buildSelectSql();
        String countSql = sqlBuilder.buildCountSql();

        log.debug("Payment summary select SQL: {}", selectSql);
        log.debug("Payment summary count SQL: {}", countSql);

        Long total = jdbcTemplate.queryForObject(countSql, params, Long.class);
        if (total == null || total == 0) {
            log.info("No payment summary records found for query: {}", query);
            return new PageImpl<>(List.of(), pageRequest, 0);
        }

        List<SearchPaymentSummaryView> content = jdbcTemplate.query(selectSql, params,
            (rs, rowNum) -> convertToPaymentSummary(rs));

        log.info("Found {} payment summary records out of {} total", content.size(), total);
        return new PageImpl<>(content, pageRequest, total);
    }

    private SearchPaymentSummaryView convertToPaymentSummary(ResultSet rs) throws SQLException {

        return SearchPaymentSummaryView.builder()
            .driverUserId(rs.getObject("driver_user_id", UUID.class))
            .driverName(rs.getString("driver_name"))
            .deliveryDate(rs.getString("delivery_date"))
            .orderId(rs.getObject("order_id", UUID.class))
            .orderNumber(rs.getString("order_number"))
            .paymentType(rs.getString("payment_type"))
            .originalOrderPrice(rs.getBigDecimal("original_total_price"))
            .finalInvoicePaidPrice(rs.getBigDecimal("total_price"))
            .returns(rs.getString("returns"))
            .shortShips(rs.getString("short_ships"))
            .notes(rs.getString("notes"))
            .build();
    }

    @Builder
    static class PaymentSummarySearchSqlBuilder {

        private PaymentSummaryQuery query;
        private Pageable pageable;
        private MapSqlParameterSource params;

        public String buildSelectSql() {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ")
                .append("dor.id as order_id, dor.order_number, ")
                .append("u.user_id as driver_user_id, u.user_name as driver_name, ")
                .append("dor.delivery_date, ")
                .append("dor.original_total_price, ")
                .append("dor.total_price, ")
                .append(
                    "CASE WHEN EXISTS (SELECT 1 FROM da_delivery_order_items doi WHERE doi.delivery_order_id = dor.id AND (doi.reason_code LIKE '%RETURNS%' OR doi.reason_code LIKE '%DAMAGED%' OR doi.reason_code LIKE '%EXPIRED%')) THEN 'Y' ELSE 'N' END as returns, ")
                .append(
                    "CASE WHEN EXISTS (SELECT 1 FROM da_delivery_order_items doi WHERE doi.delivery_order_id = dor.id AND (doi.reason_code LIKE '%MISSING%')) THEN 'Y' ELSE 'N' END as short_ships, ")
                .append("dor.payment_status, ")
                .append("dor.payment_type, ")
                .append("dor.notes ")
                .append("FROM da_delivery_order dor ")
                .append("LEFT JOIN da_delivery_task dt ON dor.delivery_task_id = dt.id ")
                .append("LEFT JOIN da_account u ON dt.driver_user_id = u.user_id ")
                .append("WHERE dor.deleted_at IS NULL ");

            sql.append(buildWhereSql());
            sql.append(buildOrderBySql());
            sql.append(" LIMIT :limit OFFSET :offset");

            params.addValue("limit", pageable.getPageSize());
            params.addValue("offset", pageable.getOffset());

            return sql.toString();
        }

        public String buildCountSql() {

            return "SELECT COUNT(*) FROM da_delivery_order dor "
                + "LEFT JOIN da_delivery_task dt ON dor.delivery_task_id = dt.id "
                + "LEFT JOIN da_account u ON dt.driver_user_id = u.user_id "
                + "WHERE dor.deleted_at IS NULL "
                + buildWhereSql();
        }

        private String buildWhereSql() {
            StringBuilder sql = new StringBuilder();

            if (StringUtils.hasText(query.getDeliveryDate())) {
                sql.append("AND dor.delivery_date = :deliveryDate ");
                params.addValue("deliveryDate", query.getDeliveryDate());
            }

            if (CollectionUtils.isNotEmpty(query.getPaymentStatus())) {
                sql.append("AND dor.payment_status in (:paymentStatus) ");
                params.addValue("paymentStatus", query.getPaymentStatus());
            }

            if (query.getIssueOrder() != null && Boolean.TRUE.equals(query.getIssueOrder())) {
                sql.append(
                        "AND ( EXISTS (SELECT 1 FROM da_delivery_order_items doi WHERE doi.delivery_order_id = dor.id AND (doi.reason_code LIKE '%RETURNS%' OR doi.reason_code LIKE '%DAMAGED%' OR doi.reason_code LIKE '%EXPIRED%')) ")
                    .append(
                        "OR EXISTS (SELECT 1 FROM da_delivery_order_items doi WHERE doi.delivery_order_id = dor.id AND (doi.reason_code LIKE '%MISSING%')) )");
            }

            return sql.toString();
        }

        private String buildOrderBySql() {
            StringBuilder sql = new StringBuilder(" ORDER BY ");
            if (pageable.getSort().isSorted()) {
                pageable.getSort()
                    .forEach(order -> {
                        String property = order.getProperty();
                        if (property.equals("deliveryDate")) {
                            property = "dor.delivery_date";
                        } else if (property.equals("paymentStatus")) {
                            property = "dor.payment_status";
                        } else if (property.equals("orderNumber")) {
                            property = "dor.order_number";
                        } else {
                            property = "dor." + property;
                        }
                        sql.append(property)
                            .append(" ")
                            .append(order.getDirection().name())
                            .append(", ");
                    });
                sql.setLength(sql.length() - 2);
            } else {
                sql.append("dor.created_at DESC");
            }
            return sql.toString();
        }
    }
}
