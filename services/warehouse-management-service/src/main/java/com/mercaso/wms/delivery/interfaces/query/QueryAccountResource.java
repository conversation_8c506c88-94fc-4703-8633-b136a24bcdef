package com.mercaso.wms.delivery.interfaces.query;

import com.mercaso.wms.delivery.application.dto.account.AccountDto;
import com.mercaso.wms.delivery.application.queryservice.AccountQueryService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Accounts")
@Slf4j
@RestController
@RequestMapping("/delivery/query/accounts")
@RequiredArgsConstructor
public class QueryAccountResource {

    private final AccountQueryService accountQueryService;

    @PreAuthorize("hasAuthority('da:read:accounts')")
    @GetMapping("/{accountId}")
    public AccountDto findById(@PathVariable UUID accountId) {
        return accountQueryService.findById(accountId);
    }

    @PreAuthorize("hasAuthority('da:read:accounts')")
    @GetMapping("/validate")
    public boolean findBy(@RequestParam String email) {
        return accountQueryService.checkAccountExist(email);
    }

    @PreAuthorize("hasAuthority('da:read:accounts')")
    @GetMapping
    public List<AccountDto> findBy(@RequestParam List<UUID> ids) {
        return accountQueryService.findBy(ids);
    }

}
