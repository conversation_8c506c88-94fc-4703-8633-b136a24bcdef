package com.mercaso.wms.domain.inventorystockhistory;

import com.mercaso.wms.domain.BaseDomain;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystockhistory.enums.TransactionEvent;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@SuperBuilder
@Configurable(preConstruction = true)
@Slf4j
public class InventoryStockHistory extends BaseDomain {

    private UUID id;

    private UUID fromStockId;

    private UUID toStockId;

    private String skuNumber;

    private String title;

    private UUID itemId;

    private Location fromLocation;

    private Location toLocation;

    private UUID entityId;

    private EntityEnums entityName;

    private String lotNumber;

    private LocalDate productionDate;

    private LocalDate expirationDate;

    private BigDecimal beforeQty;

    private BigDecimal afterQty;

    private BigDecimal changeQty;

    private UUID vendorId;

    private TransactionEvent transactionEvent;

    private String reason;

    public InventoryStockHistory receive(InventoryStock inventoryStock) {
        this.toStockId = inventoryStock.getId();
        if (inventoryStock.getItem() != null) {
            this.skuNumber = inventoryStock.getItem().getSkuNumber();
            this.title = inventoryStock.getItem().getTitle();
            this.itemId = inventoryStock.getItem().getId();
        }
        this.toLocation = inventoryStock.getLocation();
        this.lotNumber = inventoryStock.getLotNumber();
        this.productionDate = inventoryStock.getProductionDate();
        this.expirationDate = inventoryStock.getExpirationDate();
        this.beforeQty = BigDecimal.ZERO;
        this.afterQty = inventoryStock.getQty();
        this.changeQty = inventoryStock.getQty();
        this.transactionEvent = TransactionEvent.RECEIVE;
        return this;
    }

    public InventoryStockHistory createPickHistory(PickingTaskItem pickingTaskItem, Location from, Location to) {
        this.skuNumber = pickingTaskItem.getSkuNumber();
        this.title = pickingTaskItem.getTitle();
        this.itemId = pickingTaskItem.getItemId();
        this.fromLocation = from;
        this.toLocation = to;
        this.beforeQty = BigDecimal.ZERO;
        this.afterQty = BigDecimal.ZERO;
        this.changeQty = BigDecimal.valueOf(pickingTaskItem.getPickedQty());
        this.transactionEvent = TransactionEvent.PICK;
        this.entityId = pickingTaskItem.getId();
        this.entityName = EntityEnums.PICKING_TASK_ITEM;
        return this;
    }

}
