package com.mercaso.wms.infrastructure.repository.crossdock.jpa.dataobject;

import com.mercaso.wms.domain.crossdock.enums.CrossDockTaskStatusEnum;
import com.mercaso.wms.infrastructure.annotation.NumberGenerator;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.crossdockitem.jpa.dataobject.CrossDockTaskItemDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;


@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "crossDockTaskItems")
@SQLDelete(sql = "update cross_dock_task set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@Table(name = "cross_dock_task")
@NoArgsConstructor
public class CrossDockTaskDo extends BaseDo {

    @NumberGenerator
    private String number;

    @Column(name = "warehouse_id", nullable = false)
    private UUID warehouseId;

    @Column(name = "status", nullable = false)
    @Enumerated(value = EnumType.STRING)
    private CrossDockTaskStatusEnum status;

    @Column(name = "picker_user_id")
    private UUID pickerUserId;

    @Column(name = "picker_user_name")
    private String pickerUserName;

    @Column(name = "total_qty")
    private Integer totalQty;

    @Column(name = "delivery_date")
    private String deliveryDate;

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "cross_dock_task_id")
    @OrderBy("createdAt ASC")
    private List<CrossDockTaskItemDo> crossDockTaskItems;


}