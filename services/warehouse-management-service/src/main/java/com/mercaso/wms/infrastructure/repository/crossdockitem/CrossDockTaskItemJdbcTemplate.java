package com.mercaso.wms.infrastructure.repository.crossdockitem;

import com.alibaba.excel.util.StringUtils;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskItemView;
import com.mercaso.wms.application.query.CrossDockTaskItemQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@Slf4j
@Repository
@RequiredArgsConstructor
public class CrossDockTaskItemJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<SearchCrossDockTaskItemView> search(CrossDockTaskItemQuery criteria) {
        StringBuilder sql = new StringBuilder();
        StringBuilder countSql = new StringBuilder();
        MapSqlParameterSource params = new MapSqlParameterSource();

        sql.append(
                        "SELECT distinct cdt_items.id as id, cdt_items.cross_dock_task_id as cross_dock_task_id, cdt_items.batch_id as batch_id, ")
                .append("cdt_items.sku_number as sku_number, cdt_items.title as title, cdt_items.item_id as item_id, cdt_items.task_item_id as task_item_id, ")
                .append("cdt_items.source as source, cdt_items.shipping_order_id as shipping_order_id, cdt_items.shipping_order_item_id as shipping_order_item_id, ")
                .append("cdt_items.sequence as sequence, cdt_items.picked_qty as picked_qty, cdt_items.cross_docked_qty as cross_docked_qty, ")
                .append("cdt_items.breakdown_name as breakdown_name, cdt_items.created_at as created_at, cdt_items.updated_at as updated_at ")
                .append("FROM cross_dock_task_items cdt_items ")
                .append("LEFT JOIN batch b on cdt_items.batch_id = b.id ")
                .append("WHERE cdt_items.deleted_at IS NULL ");

        countSql.append("SELECT COUNT(DISTINCT cdt_items.id) ")
                .append("FROM cross_dock_task_items cdt_items ")
                .append("LEFT JOIN batch b on cdt_items.batch_id = b.id ")
                .append("WHERE cdt_items.deleted_at IS NULL ");

        if (StringUtils.isNotBlank(criteria.getSku())) {
            sql.append("AND sku_number = :sku ");
            countSql.append("AND sku_number = :sku ");
            params.addValue("sku", criteria.getSku());
        }

        if (StringUtils.isNotBlank(criteria.getDeliveryDate())) {
            sql.append("AND b.tag = :deliveryDate ");
            countSql.append("AND b.tag = :deliveryDate ");
            params.addValue("deliveryDate", criteria.getDeliveryDate());
        }

        if (criteria.getPickingTaskItemId() != null) {
            sql.append("AND task_item_id = :pickingTaskItemId ");
            countSql.append("AND task_item_id = :pickingTaskItemId ");
            params.addValue("pickingTaskItemId", criteria.getPickingTaskItemId());
        }

        if (criteria.getReceivingTaskItemId() != null) {
            sql.append("AND task_item_id = :receivingTaskItemId ");
            countSql.append("AND task_item_id = :receivingTaskItemId ");
            params.addValue("receivingTaskItemId", criteria.getReceivingTaskItemId());
        }

        int page = criteria.getPage() != null ? criteria.getPage() : 1;
        int pageSize = criteria.getPageSize() != null ? criteria.getPageSize() : 20;
        int offset = (page - 1) * pageSize;

        sql.append("ORDER BY created_at DESC ");
        sql.append("LIMIT :limit OFFSET :offset");
        params.addValue("limit", pageSize);
        params.addValue("offset", offset);

        Long total = jdbcTemplate.queryForObject(countSql.toString(), params, Long.class);
        List<SearchCrossDockTaskItemView> content = jdbcTemplate.query(sql.toString(), params, (rs, rowNum) -> mapToView(rs));
        Pageable pageable = PageRequest.of(page - 1, pageSize);
        return new PageImpl<>(content, pageable, total != null ? total : 0);
    }

    private SearchCrossDockTaskItemView mapToView(ResultSet rs) throws SQLException {
        SearchCrossDockTaskItemView view = new SearchCrossDockTaskItemView();
        view.setId(rs.getObject("id", java.util.UUID.class));
        view.setCrossDockTaskId(rs.getObject("cross_dock_task_id", java.util.UUID.class));
        view.setBatchId(rs.getObject("batch_id", java.util.UUID.class));
        view.setSkuNumber(rs.getString("sku_number"));
        view.setTitle(rs.getString("title"));
        view.setItemId(rs.getObject("item_id", java.util.UUID.class));
        view.setTaskItemId(rs.getObject("task_item_id", java.util.UUID.class));
        view.setSource(rs.getString("source") == null ? null : com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum.valueOf(rs.getString("source")));
        view.setShippingOrderId(rs.getObject("shipping_order_id", java.util.UUID.class));
        view.setShippingOrderItemId(rs.getObject("shipping_order_item_id", java.util.UUID.class));
        java.sql.Array sequenceArray = rs.getArray("sequence");
        view.setSequence(sequenceArray == null ? java.util.Collections.emptyList() : java.util.Arrays.asList((String[]) sequenceArray.getArray()));
        view.setPickedQty(rs.getObject("picked_qty") == null ? null : rs.getInt("picked_qty"));
        view.setCrossDockedQty(rs.getObject("cross_docked_qty") == null ? null : rs.getInt("cross_docked_qty"));
        view.setBreakdownName(rs.getString("breakdown_name"));
        return view;
    }
} 