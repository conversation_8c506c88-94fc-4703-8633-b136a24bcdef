package com.mercaso.wms.infrastructure.repository.transfertaskitems.jpa;

import com.mercaso.wms.infrastructure.repository.transfertaskitems.jpa.dataobject.TransferTaskItemDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface TransferTaskItemJpaDao extends JpaRepository<TransferTaskItemDo, UUID> {

    List<TransferTaskItemDo> findByPickingTaskItemId(UUID pickingTaskItemId);
}