package com.mercaso.wms.interfaces;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.ims.client.dto.ItemSerachDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderAdminDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/shopify")
@RequiredArgsConstructor
public class ShopifyWebhookResource {

    @Value("${shopify.webhook.domain}")
    private String shopifyWebhookDomain;

    private final ShippingOrderApplicationService shippingOrderApplicationService;

    private final ShopifyAdaptor shopifyAdaptor;

    private final ImsAdaptor imsAdaptor;

    @PostMapping("/webhook")
    public void handleWebhook(@RequestBody String payload,
        @RequestHeader("X-Shopify-Topic") String topic,
        @RequestHeader("X-Shopify-Shop-Domain") String shopDomain) {
        log.debug("Received webhook payload: {}", payload);
        if (!shopDomain.equals(shopifyWebhookDomain)) {
            log.warn("Received webhook for unknown domain: {}", shopDomain);
            return;
        }
        try {
            ShopifyOrderDto shopifyOrderDto = SerializationUtils.readValue(payload, new TypeReference<ShopifyOrderDto>() {
            });

            boolean hasNullSku = shopifyOrderDto.getLineItems().stream().anyMatch(item -> item.getSku() == null);
            if (hasNullSku) {
                ShopifyOrderAdminDto shopifyOrderByAdminApi = shopifyAdaptor.getShopifyOrderByAdminApi(shopifyOrderDto.getId());
                shopifyOrderDto = shopifyOrderByAdminApi.getOrder();
                shopifyOrderDto.getLineItems().forEach(item -> {
                    if (item.getSku() == null) {
                        ItemSerachDto itemSerachDto = imsAdaptor.searchItemsByTitle(item.getTitle());
                        if (itemSerachDto != null) {
                            item.setSku(itemSerachDto.getSkuNumber());
                        } else {
                            log.error("No SKU found for item with title: {}", item.getTitle());
                        }
                    }
                });
            }
            log.info("Received shopify order: {} {}", shopifyOrderDto.getName(), shopifyOrderDto.getId());
            shippingOrderApplicationService.createOrUpdate(shopifyOrderDto);
        } catch (IOException e) {
            String orderId = getOrderId(payload);
            if (orderId != null) {
                ShopifyOrderAdminDto shopifyOrderDto = shopifyAdaptor.getShopifyOrderByAdminApi(orderId);
                shippingOrderApplicationService.createOrUpdate(shopifyOrderDto.getOrder());
            } else {
                log.error("Failed to deserialize Shopify webhook payload: {}", payload);
            }
        }
    }

    private String getOrderId(String payload) {
        String regex = "\"id\":\"(\\d+)\"";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(payload);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

}
