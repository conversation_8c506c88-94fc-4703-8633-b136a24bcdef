package com.mercaso.wms.interfaces.query;

import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.queryservice.PickingTaskQueryService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/query/picking-tasks")
@RequiredArgsConstructor
public class QueryPickingTaskResource {

    private final PickingTaskQueryService pickingTaskQueryService;

    @PreAuthorize("hasAuthority('wms:read:picking-tasks')")
    @GetMapping("/{id}")
    public PickingTaskDto findById(@PathVariable UUID id) {
        return pickingTaskQueryService.findById(id);
    }

}
