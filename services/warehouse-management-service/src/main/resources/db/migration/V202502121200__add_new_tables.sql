CREATE TABLE if not exists transfer_task
(
    id                       UUID PRIMARY KEY,
    number                   VARCHAR(20) NOT NULL,
    status                   VARCHAR(20) NOT NULL,
    origin_warehouse_id      UUID        NOT NULL,
    destination_warehouse_id UUID        NOT NULL,
    loader_user_id           UUID,
    loader_user_name         <PERSON><PERSON><PERSON><PERSON>(50),
    receiver_user_id         UUID,
    receiver_user_name       VA<PERSON>HAR(50),
    loading_at               TIMESTAMP,
    received_at              TIMESTAMP,
    created_at               TIMESTAMP   NOT NULL DEFAULT NOW(),
    created_by               VA<PERSON><PERSON>R(100),
    updated_at               TIMESTAMP   NOT NULL DEFAULT NOW(),
    updated_by               <PERSON><PERSON><PERSON><PERSON>(100),
    deleted_at               TIMESTAMP,
    deleted_by               VARCHAR(100)
);
CREATE INDEX if not exists transfer_task_number_idx ON transfer_task (number);
CREATE INDEX if not exists transfer_task_loader_user_id_idx ON transfer_task (loader_user_id);
CREATE INDEX if not exists transfer_task_receiver_user_id_idx ON transfer_task (receiver_user_id);

COMMENT ON COLUMN transfer_task.id IS 'Primary key for the transfer_task table';
COMMENT ON COLUMN transfer_task.number IS 'The unique number for the transfer task, unique code: T-XXXXXX';
COMMENT ON COLUMN transfer_task.status IS 'Statuses: draft,awaiting_loading,awaiting_receiving,received';
COMMENT ON COLUMN transfer_task.origin_warehouse_id IS 'The ID of the origin warehouse';
COMMENT ON COLUMN transfer_task.destination_warehouse_id IS 'The ID of the destination warehouse';
COMMENT ON COLUMN transfer_task.loader_user_id IS 'The ID of the user who loaded the transfer';
COMMENT ON COLUMN transfer_task.loader_user_name IS 'The name of the user who loaded the transfer';
COMMENT ON COLUMN transfer_task.receiver_user_id IS 'The ID of the user who received the transfer';
COMMENT ON COLUMN transfer_task.receiver_user_name IS 'The name of the user who received the transfer';
COMMENT ON COLUMN transfer_task.loading_at IS 'The timestamp when the transfer was loaded';
COMMENT ON COLUMN transfer_task.received_at IS 'The timestamp when the transfer was received';
COMMENT ON COLUMN transfer_task.created_at IS 'The timestamp when the transfer task was created';
COMMENT ON COLUMN transfer_task.created_by IS 'The user who created the transfer task';
COMMENT ON COLUMN transfer_task.updated_at IS 'The timestamp when the transfer task was last updated';
COMMENT ON COLUMN transfer_task.updated_by IS 'The user who last updated the transfer task';
COMMENT ON COLUMN transfer_task.deleted_at IS 'The timestamp when the transfer task was deleted';
COMMENT ON COLUMN transfer_task.deleted_by IS 'The user who deleted the transfer task';


CREATE TABLE if not exists transfer_task_items
(
    id                      UUID PRIMARY KEY,
    transfer_task_id        UUID      NOT NULL,
    item_id                 UUID,
    sku_number              VARCHAR(20),
    title                   VARCHAR,
    picking_task_item_id    UUID      NOT NULL,
    origin_location_id      UUID,
    destination_location_id UUID,
    transfer_qty            INT,
    created_at              TIMESTAMP NOT NULL DEFAULT NOW(),
    created_by              VARCHAR(100),
    updated_at              TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_by              VARCHAR(100),
    deleted_at              TIMESTAMP,
    deleted_by              VARCHAR(100)
);
CREATE INDEX if not exists transfer_task_items_transfer_task_id_idx ON transfer_task_items (transfer_task_id);

-- Comments for transfer_task_items table
COMMENT ON COLUMN transfer_task_items.id IS 'Primary key for the transfer_task_items table';
COMMENT ON COLUMN transfer_task_items.transfer_task_id IS 'The ID of the associated transfer task';
COMMENT ON COLUMN transfer_task_items.item_id IS 'The ID of the item being transferred';
COMMENT ON COLUMN transfer_task_items.sku_number IS 'The SKU number of the item being transferred';
COMMENT ON COLUMN transfer_task_items.title IS 'The title of the item being transferred';
COMMENT ON COLUMN transfer_task_items.picking_task_item_id IS 'The ID of the associated picking task item';
COMMENT ON COLUMN transfer_task_items.origin_location_id IS 'The ID of the origin location';
COMMENT ON COLUMN transfer_task_items.destination_location_id IS 'The ID of the destination location';
COMMENT ON COLUMN transfer_task_items.transfer_qty IS 'The quantity of the item being transferred';
COMMENT ON COLUMN transfer_task_items.created_at IS 'The timestamp when the transfer task item was created';
COMMENT ON COLUMN transfer_task_items.created_by IS 'The user who created the transfer task item';
COMMENT ON COLUMN transfer_task_items.updated_at IS 'The timestamp when the transfer task item was last updated';
COMMENT ON COLUMN transfer_task_items.updated_by IS 'The user who last updated the transfer task item';
COMMENT ON COLUMN transfer_task_items.deleted_at IS 'The timestamp when the transfer task item was deleted';
COMMENT ON COLUMN transfer_task_items.deleted_by IS 'The user who deleted the transfer task item';

ALTER table location add column if not exists finale_id varchar(50);
